import 'package:flutter/material.dart';
import 'package:showcaseview/showcaseview.dart';

/// Diese Klasse stellt einheitliche Styling-Funktionen für alle Onboarding-Elemente bereit
class OnboardingStyleHelper {
  /// Primäre Farbe für das Onboarding
  static Color getPrimaryColor(BuildContext context) {
    return Theme.of(context).colorScheme.primary;
  }

  /// Sekundäre Farbe für das Onboarding
  static Color getSecondaryColor(BuildContext context) {
    return Theme.of(context).colorScheme.secondary;
  }

  /// Hintergrundfarbe für Onboarding-Karten
  static Color getCardColor(BuildContext context) {
    return Colors.white;
  }

  /// Schatten für Onboarding-Karten
  static List<BoxShadow> getCardShadow() {
    return [
      BoxShadow(
        color: Colors.black.withAlpha(20),
        blurRadius: 10,
        spreadRadius: 1,
      ),
    ];
  }

  /// Border-Radius für Onboarding-Karten
  static BorderRadius getCardBorderRadius() {
    return BorderRadius.circular(16);
  }

  /// Gradient für Onboarding-Hintergründe
  static Gradient getBackgroundGradient(BuildContext context) {
    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        getPrimaryColor(context),
        getPrimaryColor(context).withAlpha(150),
      ],
    );
  }

  /// Textstil für Onboarding-Titel
  static TextStyle getTitleTextStyle(BuildContext context) {
    return TextStyle(
      color: Colors.black87,
      fontSize: 20,
      fontWeight: FontWeight.bold,
    );
  }

  /// Textstil für Onboarding-Beschreibungen
  static TextStyle getDescriptionTextStyle(BuildContext context) {
    return TextStyle(
      color: Colors.black54,
      fontSize: 16,
    );
  }

  /// Erstellt einen einheitlichen ShowcaseView
  static Widget buildUnifiedShowcase({
    required GlobalKey key,
    required String title,
    required String description,
    required Widget child,
    TooltipPosition? tooltipPosition,
  }) {
    return Showcase(
      key: key,
      title: title,
      description: description,
      child: child,
      tooltipPosition: tooltipPosition,
      titleTextStyle: TextStyle(
        color: Colors.white,
        fontSize: 20,
        fontWeight: FontWeight.bold,
      ),
      descTextStyle: TextStyle(
        color: Colors.white,
        fontSize: 16,
      ),
      overlayOpacity: 0.7,
      tooltipPadding: const EdgeInsets.all(16.0),
      tooltipBackgroundColor: Colors.blueGrey.shade700,
      showArrow: true,
      targetShapeBorder: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.0),
      ),
    );
  }

  /// Erstellt eine Onboarding-Card mit einheitlichem Design
  static Widget buildOnboardingCard({
    required BuildContext context,
    required Widget child,
    EdgeInsets? padding,
  }) {
    return Container(
      padding: padding ?? EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: getCardColor(context),
        borderRadius: getCardBorderRadius(),
        boxShadow: getCardShadow(),
      ),
      child: child,
    );
  }

  /// Erstellt einen einheitlichen Onboarding-Button
  static Widget buildOnboardingButton({
    required BuildContext context,
    required String text,
    required VoidCallback onPressed,
    bool isPrimary = true,
  }) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor:
            isPrimary ? getPrimaryColor(context) : Colors.grey.shade200,
        foregroundColor: isPrimary ? Colors.white : Colors.black87,
        padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(30),
        ),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}
