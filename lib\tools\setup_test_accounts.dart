import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';

/// Dieses Tool richtet Test-Accounts für das Abonnement-System in Firestore ein.
/// Es erstellt eine Sammlung "settings" mit einem Dokument "test_accounts",
/// das ein Array "accounts" mit den angegebenen E-Mail-Adressen enthält.
Future<void> main() async {
  // Initialisierung von Flutter und Firebase
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();

  // Zu speichernde E-Mail-Adressen
  final List<String> testAccounts = [
    '<EMAIL>',
    '<EMAIL>',
  ];

  // Status der Operation
  bool success = false;
  String message = '';

  try {
    // Zugriff auf Firestore
    final FirebaseFirestore firestore = FirebaseFirestore.instance;

    // Prüfen, ob das Dokument bereits existiert
    final docRef = firestore.collection('settings').doc('test_accounts');
    final docSnapshot = await docRef.get();

    if (docSnapshot.exists) {
      // Dokument aktualisieren
      await docRef.update({'accounts': testAccounts});
      message = 'Test-Accounts erfolgreich aktualisiert: $testAccounts';
    } else {
      // Dokument erstellen
      await docRef.set({'accounts': testAccounts});
      message = 'Test-Accounts erfolgreich erstellt: $testAccounts';
    }

    success = true;
  } catch (e) {
    message = 'Fehler beim Einrichten der Test-Accounts: $e';
  }

  // Ausgabe des Ergebnisses
  print(message);

  // Erfolgsstatus ausgeben und Programm beenden
  if (success) {
    print('✅ Einrichtung abgeschlossen.');
  } else {
    print('❌ Einrichtung fehlgeschlagen.');
  }

  // Beenden der App nach Abschluss
  // Wir benötigen einen kleinen Verzögerung, um sicherzustellen, dass alle Firebase-Operationen abgeschlossen sind
  await Future.delayed(Duration(seconds: 2));
  
  // App beenden
  // ignore: avoid_print
  print('Programm wird beendet...');
}
