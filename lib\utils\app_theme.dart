import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart'; // Import bleibt gleich

class AppTheme {
  AppTheme._(); // privater Konstruktor (nur statische Nutzung)

  // --- Farben ---

  // Primär- und Sekundärfarben
  static const Color _primaryColor = Color(0xFF009688); // Türkis
  static const Color _secondaryColor = Color(0xFF264653); // Dunkles Blaugrün

  // Hintergrund- und Flächenfarben
  static const Color _backgroundColor =
      Color.fromARGB(255, 246, 245, 245); // Helles Grauweiß
  static const Color _surfaceColor = Colors.white; // Wei<PERSON> für <PERSON>rten etc.

  // Textfarben
  // -> ÄNDERE HIER DIE ALLGEMEINE SCHRIFTFARBE <-
  static const Color _textColorPrimary =
      Colors.black87; // Haupttextfarbe (fast Schwarz)
  static const Color _textColorSecondary =
      Colors.black54; // Sekundärtextfarbe (Grau)

  // Akzentfarben
  static const Color selectedItemColor =
      Color(0xFFE0F2F1); // Sehr helles Türkis für Auswahl
  static final Color _dividerColor =
      Colors.grey.shade100; // Helleres Grau für Divider

  // --- ColorScheme (Basis für Material 3 Komponenten) ---
  static final ColorScheme _colorScheme = ColorScheme(
    brightness: Brightness.light,
    primary: _primaryColor,
    onPrimary: Colors.white, // Text/Icons auf Primärfarbe
    secondary: _secondaryColor,
    onSecondary: Colors.white, // Text/Icons auf Sekundärfarbe
    error: Colors.red.shade700, // Standard Rot für Fehler
    onError: Colors.white, // Text/Icons auf Fehlerfarbe
    background: _backgroundColor,
    onBackground: _textColorPrimary, // Standard Text/Icons auf Hintergrund
    surface: _surfaceColor,
    onSurface:
        _textColorPrimary, // Standard Text/Icons auf Oberflächen (Karten)
    // Container-Farben (oft für Hintergründe von Buttons, Chips etc. verwendet)
    primaryContainer: _primaryColor.withOpacity(0.1),
    onPrimaryContainer: _primaryColor,
    secondaryContainer: _secondaryColor.withOpacity(0.1),
    onSecondaryContainer: _secondaryColor,
    // Weitere Farben könnten hier definiert werden (surfaceVariant, outline, etc.)
  );

  // --- Text Stile (optional, für Konsistenz und Wiederverwendung) ---
  // NEU: headlineSmall als Variable definiert
  static final TextStyle _headlineSmallStyle = GoogleFonts.roboto(
    fontSize: 18.0, // Typische Größe für headlineSmall
    fontWeight: FontWeight.w600, // Etwas fetter als normaler Text
    color: _textColorPrimary, // Nutzt die primäre Textfarbe
    letterSpacing: 0.15, // Optional: Leichter Zeichenabstand
  );

  // Definition für labelSmall (bereits vorhanden, zur Übersicht hier)
  static final TextStyle _labelSmallStyle = TextStyle(
    color: Colors.grey.shade600, // Etwas dunkleres Grau als Divider
    fontWeight: FontWeight.bold,
    fontSize: 11,
    letterSpacing: 0.8, // Leichter Abstand zwischen Buchstaben
  );

  // --- ThemeData Definition ---
  static final ThemeData lightTheme = ThemeData(
      useMaterial3: true, // Material 3 aktivieren
      colorScheme: _colorScheme, // Das definierte Farbschema verwenden
      scaffoldBackgroundColor: _backgroundColor, // Hintergrund für Scaffold
      canvasColor:
          _surfaceColor, // Standardhintergrund für Drawer, Dialoge etc.

      // --- Komponentenspezifische Themes ---

      // Globales Icon-Theme (Standardgröße und -farbe)
      iconTheme: IconThemeData(
        color: _textColorSecondary, // Standard Icon Farbe (Grau)
        size: 22.0, // Leicht reduzierte Standardgröße
      ),

      // AppBar-Theme
      appBarTheme: AppBarTheme(
        backgroundColor: _colorScheme.primary, // AppBar Hintergrund
        foregroundColor:
            _colorScheme.onPrimary, // Titel- und Iconfarbe in AppBar
        elevation: 0, // Kein Schatten
        iconTheme:
            IconThemeData(color: _colorScheme.onPrimary), // Iconfarbe in AppBar
        titleTextStyle: GoogleFonts.roboto(
          // Schriftart für AppBar-Titel
          color: _colorScheme.onPrimary,
          fontSize: 20,
          fontWeight: FontWeight.w600, // Halbfett
        ),
      ),

      // TextTheme (Schriftarten und Standardfarben)
      textTheme: GoogleFonts.robotoTextTheme(
        // Basis-Schriftart Roboto
        ThemeData.light().textTheme.apply(
              // Farben auf Standard-Flutter-Theme anwenden
              // -> HIER WERDEN DIE ALLGEMEINEN TEXTFARBEN GESETZT <-
              bodyColor: _textColorPrimary, // Standardfarbe für normalen Text
              displayColor:
                  _textColorPrimary, // Standardfarbe für Überschriften etc.
            ),
      ).copyWith(
        // Spezifische Text Stile anpassen oder hinzufügen
        labelSmall: _labelSmallStyle, // Nutzt die definierte Variable

        // NEU: headlineSmall hinzugefügt und die Variable verwendet
        headlineSmall: _headlineSmallStyle,

        // Beispiel: Anderen Stil anpassen
        // bodyMedium: TextStyle(fontSize: 15, color: Colors.purple),
      ),

      // Button-Themes
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: _colorScheme.primary, // Hintergrund
          foregroundColor: _colorScheme.onPrimary, // Textfarbe
          textStyle: GoogleFonts.roboto(fontWeight: FontWeight.w600), // Schrift
          padding: const EdgeInsets.symmetric(
              vertical: 14.0, horizontal: 24.0), // Innenabstand
          shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8)), // Abgerundete Ecken
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: _colorScheme.primary, // Textfarbe
          textStyle: GoogleFonts.roboto(fontWeight: FontWeight.w600),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: _colorScheme.primary, // Text- und Rahmenfarbe
          side: BorderSide(
              color: _colorScheme.primary
                  .withOpacity(0.5)), // Leicht transparenter Rahmen
          textStyle: GoogleFonts.roboto(fontWeight: FontWeight.w600),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      ),

      // Card-Theme
      cardTheme: CardTheme(
        color: _surfaceColor, // Kartenhintergrund
        elevation: 0.2, // Kein Schatten (wie im Screenshot)
        margin: const EdgeInsets.symmetric(
            horizontal: 8.0, vertical: 4.0), // Standardabstand
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12), // Abgerundete Ecken
            side: BorderSide(
                color: Colors.grey.shade300, width: 1)), // Heller Rahmen
      ),

      // InputDecoration Theme (für TextFormFields)
      inputDecorationTheme: InputDecorationTheme(
        filled: true, // Hintergrund füllen
        fillColor: Colors.white, // Hintergrundfarbe
        contentPadding: const EdgeInsets.symmetric(
            vertical: 12.0, horizontal: 16.0), // Innenabstand
        // Rahmenstile
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Colors.grey.shade300), // Standardrahmen
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide:
              BorderSide(color: Colors.grey.shade300), // Rahmen, wenn aktiv
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(
              color: _primaryColor, width: 1.5), // Rahmen, wenn fokussiert
        ),
        // Textstile für Hinweise und Labels
        hintStyle: GoogleFonts.roboto(
            color: Colors.grey.shade500), // Heller für Hinweise
        labelStyle: GoogleFonts.roboto(
            color: Colors.grey.shade700), // Etwas dunkler für Labels
        // Iconfarben
        prefixIconColor: _textColorSecondary,
        suffixIconColor: _textColorSecondary,
      ),

      // FloatingActionButton Theme
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: _colorScheme.primary,
        foregroundColor: _colorScheme.onPrimary,
        elevation: 4,
      ),

      // ListTile Theme (wichtig für Drawer Items)
      listTileTheme: ListTileThemeData(
        selectedColor: _colorScheme.primary, // Text/Iconfarbe, wenn ausgewählt
        selectedTileColor:
            selectedItemColor, // Hintergrundfarbe, wenn ausgewählt
        iconColor: _textColorSecondary, // Standard Icon Farbe in ListTiles
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8)), // Abrundung
        dense: true, // Kompakteres Layout
        horizontalTitleGap: 10.0, // Abstand Icon <-> Text
      ),

      // Divider Theme (Global) - DIES SOLLTE GREIFEN!
      dividerTheme: DividerThemeData(
        color: _dividerColor, // Heller definierte Farbe
        thickness: 1, // Dicke
        space: 1, // Minimaler vertikaler Platz, den der Divider einnimmt
        indent: 16, // Einzug links
        endIndent: 16, // Einzug rechts
      ),

      // DropdownMenu Theme
      dropdownMenuTheme: DropdownMenuThemeData(
          textStyle: GoogleFonts.roboto(color: _textColorPrimary),
          inputDecorationTheme: InputDecorationTheme(
            // Style für das Dropdown-Feld selbst
            filled: true,
            fillColor: Colors.white,
            contentPadding:
                const EdgeInsets.symmetric(vertical: 10.0, horizontal: 12.0),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: _primaryColor, width: 1.5),
            ),
            prefixIconColor: _textColorSecondary,
            suffixIconColor: _textColorSecondary,
          )));
}
