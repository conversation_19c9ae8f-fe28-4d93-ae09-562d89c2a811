import 'package:flutter/material.dart';
import 'package:showcaseview/showcaseview.dart';
import '../widgets/showcase_helper.dart';
import '../services/onboarding_service.dart';

/// Klasse zur Verwaltung des CashierScreen-Onboardings mit einheitlichem Design
class CashierScreenOnboarding {
  final BuildContext context;
  final GlobalKey addButtonKey = GlobalKey();
  final GlobalKey cashierListKey = GlobalKey();
  final GlobalKey cashierSelectionKey = GlobalKey();

  CashierScreenOnboarding({
    required this.context,
  });

  /// Startet das CashierScreen-Onboarding
  Future<void> startOnboarding() async {
    final onboardingService = OnboardingService();
    final isCompleted =
        await onboardingService.isCashierScreenOnboardingCompleted();

    if (isCompleted) {
      debugPrint('CashierScreenOnboarding: Onboarding bereits abgeschlossen');
      return;
    }

    // Warte einen Moment, damit die UI vollständig gerendert ist
    await Future.delayed(const Duration(milliseconds: 500));

    // Starte das Onboarding
    _showCashierOverview();
  }

  /// Zeigt den Überblick über den CashierScreen
  void _showCashierOverview() {
    final keys = [addButtonKey, cashierListKey, cashierSelectionKey];

    ShowcaseHelper.showMultipleShowcase(
      context,
      keys,
      onFinish: () {
        // Onboarding als abgeschlossen markieren
        _completeOnboarding();
      },
    );
  }

  /// Markiert das CashierScreen-Onboarding als abgeschlossen
  Future<void> _completeOnboarding() async {
    final onboardingService = OnboardingService();
    await onboardingService.setCashierScreenOnboardingCompleted(true);
    debugPrint('CashierScreenOnboarding: Onboarding abgeschlossen');
  }

  /// Baut die Showcase-Widgets für den CashierScreen
  List<Widget> buildShowcaseWidgets({
    required Widget addButtonWidget,
    required Widget cashierListWidget,
    required Widget cashierSelectionWidget,
  }) {
    return [
      // Hinzufügen-Button
      ShowcaseHelper.buildShowcase(
        key: addButtonKey,
        context: context,
        title: 'Neuen Kassierer anlegen',
        description:
            'Hier können Sie einen neuen Kassierer erstellen. Die Kassierernummer wird automatisch vergeben.',
        child: addButtonWidget,
      ),

      // Kassiererliste
      ShowcaseHelper.buildShowcase(
        key: cashierListKey,
        context: context,
        title: 'Kassiererliste',
        description:
            'Hier sehen Sie alle angelegten Kassierer. Sie können Kassierer bearbeiten oder löschen.',
        child: cashierListWidget,
      ),

      // Kassierer-Auswahl
      ShowcaseHelper.buildShowcase(
        key: cashierSelectionKey,
        context: context,
        title: 'Kassierer-Auswahl',
        description:
            'Jeder Kassierer sollte sich zu Beginn seiner Schicht anmelden. Die Buchungen werden dem ausgewählten Kassierer zugeordnet.',
        child: cashierSelectionWidget,
        tooltipPosition: TooltipPosition.top,
      ),
    ];
  }
}
