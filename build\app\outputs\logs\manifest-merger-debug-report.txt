-- Merging decision tree log ---
application
INJECTED from C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\main\AndroidManifest.xml:12:5-66:19
INJECTED from C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\debug\AndroidManifest.xml
MERGED from [:cloud_firestore] C:\Users\<USER>\Documents\environments\troedelpos\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-13:19
MERGED from [:cloud_firestore] C:\Users\<USER>\Documents\environments\troedelpos\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_auth] C:\Users\<USER>\Documents\environments\troedelpos\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_auth] C:\Users\<USER>\Documents\environments\troedelpos\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_core] C:\Users\<USER>\Documents\environments\troedelpos\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_core] C:\Users\<USER>\Documents\environments\troedelpos\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-13:19
MERGED from [:share_plus] C:\Users\<USER>\Documents\environments\troedelpos\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-33:19
MERGED from [:share_plus] C:\Users\<USER>\Documents\environments\troedelpos\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-33:19
MERGED from [:url_launcher_android] C:\Users\<USER>\Documents\environments\troedelpos\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-12:19
MERGED from [:url_launcher_android] C:\Users\<USER>\Documents\environments\troedelpos\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-12:19
MERGED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:28:5-73:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\69a6d671be6bdd1a1fdb40a1fed71722\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\69a6d671be6bdd1a1fdb40a1fed71722\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [com.google.mlkit:digital-ink-recognition:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8fc52eb327e2202155f3dbc243d8187a\transformed\jetified-digital-ink-recognition-18.1.0\AndroidManifest.xml:11:5-19:19
MERGED from [com.google.mlkit:digital-ink-recognition:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8fc52eb327e2202155f3dbc243d8187a\transformed\jetified-digital-ink-recognition-18.1.0\AndroidManifest.xml:11:5-19:19
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:30:5-143:19
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:30:5-143:19
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition-common:19.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed51e9ea00ee12a4c2d9fc12ca35b3c2\transformed\jetified-play-services-mlkit-text-recognition-common-19.1.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition-common:19.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed51e9ea00ee12a4c2d9fc12ca35b3c2\transformed\jetified-play-services-mlkit-text-recognition-common-19.1.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\e62b8171866d636f8ed6a0bfd220b8f2\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\e62b8171866d636f8ed6a0bfd220b8f2\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\811d866da11c4adc1e237c7be48ea17a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:23:5-42:19
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\811d866da11c4adc1e237c7be48ea17a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:23:5-42:19
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3998f03731776e641b0412496162b18\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3998f03731776e641b0412496162b18\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f79562eedfa06deadff6479d4f4acac\transformed\jetified-billing-7.1.1\AndroidManifest.xml:21:5-36:19
MERGED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f79562eedfa06deadff6479d4f4acac\transformed\jetified-billing-7.1.1\AndroidManifest.xml:21:5-36:19
MERGED from [com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\a2bc3fa9b88a89bfa05736f8fd4a3121\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\a2bc3fa9b88a89bfa05736f8fd4a3121\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\076acdf74b13b0d0be55167d9cf30c82\transformed\jetified-common-18.11.0\AndroidManifest.xml:8:5-24:19
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\076acdf74b13b0d0be55167d9cf30c82\transformed\jetified-common-18.11.0\AndroidManifest.xml:8:5-24:19
MERGED from [com.google.android.gms:play-services-location:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\946c41ae743610321d53df650d1a1a0d\transformed\jetified-play-services-location-19.0.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\946c41ae743610321d53df650d1a1a0d\transformed\jetified-play-services-location-19.0.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\447162408152222549d7f79b7bf5f55b\transformed\jetified-firebase-appcheck-interop-17.0.0\AndroidManifest.xml:23:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\447162408152222549d7f79b7bf5f55b\transformed\jetified-firebase-appcheck-interop-17.0.0\AndroidManifest.xml:23:5-20
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e42a5d0b6bf6f23c86f61473a141749\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e42a5d0b6bf6f23c86f61473a141749\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b368049dbdfe7c192fa848707b37b6\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b368049dbdfe7c192fa848707b37b6\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f5a019bffc1f9b189f3d5a06719dd484\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f5a019bffc1f9b189f3d5a06719dd484\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\550b14c5117666deb34b8cf6b1829c33\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\550b14c5117666deb34b8cf6b1829c33\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3bb114678107da1ef99f97798dc193bb\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3bb114678107da1ef99f97798dc193bb\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\b9e27e61055dde67f8637b8f89a8f0a0\transformed\jetified-integrity-1.3.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\b9e27e61055dde67f8637b8f89a8f0a0\transformed\jetified-integrity-1.3.0\AndroidManifest.xml:5:5-6:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\cb4f9b7546ddd8ec180df336ad808fa4\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\cb4f9b7546ddd8ec180df336ad808fa4\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\952d74a4f84de990b1d8333e6126b61e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\952d74a4f84de990b1d8333e6126b61e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c7904ef7c3d667722357509ea858f00b\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c7904ef7c3d667722357509ea858f00b\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-places-placereport:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a8b9c038a2e82e8e93ea2f0161633e5c\transformed\jetified-play-services-places-placereport-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-places-placereport:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a8b9c038a2e82e8e93ea2f0161633e5c\transformed\jetified-play-services-places-placereport-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\19d4c2635369e2aa123936ad1ab3a080\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\19d4c2635369e2aa123936ad1ab3a080\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\fe930d2507b0279f955567b6d5748a32\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\fe930d2507b0279f955567b6d5748a32\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\5366afd6a5dea0a6abfe5e8efa6b7106\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\5366afd6a5dea0a6abfe5e8efa6b7106\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b740f3eec8188e2f93c4f646edb23e95\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b740f3eec8188e2f93c4f646edb23e95\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\5dc29b04cf00cdbc8783e786639e2a69\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\5dc29b04cf00cdbc8783e786639e2a69\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\ee969d6aefaa82cfa2f0e33fc4ab16ac\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\ee969d6aefaa82cfa2f0e33fc4ab16ac\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:25:5-39:19
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\e3cf089486f7ad4e20f60677fe72bdf6\transformed\room-runtime-2.2.5\AndroidManifest.xml:24:5-29:19
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\e3cf089486f7ad4e20f60677fe72bdf6\transformed\room-runtime-2.2.5\AndroidManifest.xml:24:5-29:19
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\transforms-3\3d937d2078c6e546e68ff6956c0cf58b\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\transforms-3\3d937d2078c6e546e68ff6956c0cf58b\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\17add4f136784dea2dd439689278c96a\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:11:5-19:19
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\17add4f136784dea2dd439689278c96a\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:11:5-19:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\fe930d2507b0279f955567b6d5748a32\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:name
		INJECTED from C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\main\AndroidManifest.xml
manifest
ADDED from C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\main\AndroidManifest.xml:1:1-77:12
MERGED from C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\main\AndroidManifest.xml:1:1-77:12
INJECTED from C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\debug\AndroidManifest.xml:1:1-7:12
MERGED from [:camera_android] C:\Users\<USER>\Documents\environments\troedelpos\build\camera_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-10:12
MERGED from [:cloud_firestore] C:\Users\<USER>\Documents\environments\troedelpos\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-15:12
MERGED from [:connectivity_plus] C:\Users\<USER>\Documents\environments\troedelpos\build\connectivity_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:firebase_auth] C:\Users\<USER>\Documents\environments\troedelpos\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-15:12
MERGED from [:firebase_core] C:\Users\<USER>\Documents\environments\troedelpos\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-15:12
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\Documents\environments\troedelpos\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:flutter_secure_storage] C:\Users\<USER>\Documents\environments\troedelpos\build\flutter_secure_storage\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-10:12
MERGED from [:google_mlkit_digital_ink_recognition] C:\Users\<USER>\Documents\environments\troedelpos\build\google_mlkit_digital_ink_recognition\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:google_mlkit_text_recognition] C:\Users\<USER>\Documents\environments\troedelpos\build\google_mlkit_text_recognition\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:google_mlkit_commons] C:\Users\<USER>\Documents\environments\troedelpos\build\google_mlkit_commons\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:google_sign_in_android] C:\Users\<USER>\Documents\environments\troedelpos\build\google_sign_in_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:in_app_purchase_android] C:\Users\<USER>\Documents\environments\troedelpos\build\in_app_purchase_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:path_provider_android] C:\Users\<USER>\Documents\environments\troedelpos\build\path_provider_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:permission_handler_android] C:\Users\<USER>\Documents\environments\troedelpos\build\permission_handler_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:share_plus] C:\Users\<USER>\Documents\environments\troedelpos\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-35:12
MERGED from [:shared_preferences_android] C:\Users\<USER>\Documents\environments\troedelpos\build\shared_preferences_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:sqflite_android] C:\Users\<USER>\Documents\environments\troedelpos\build\sqflite_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:url_launcher_android] C:\Users\<USER>\Documents\environments\troedelpos\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-14:12
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a75011f7f88673231c312008696729f9\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:17:1-75:12
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f6219dea7a69dbc6d2bf4921723a7d5\transformed\browser-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\88a95dc090f8ff91430f7b0681cf077e\transformed\recyclerview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\93e6004bff5aedd63abbe49bb91b469e\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6811d104c5a3aa633077b6f7528fd5be\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\69a6d671be6bdd1a1fdb40a1fed71722\transformed\jetified-window-1.2.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\636e11c147967ac5df8d2d450eee6ea7\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:17:1-21:12
MERGED from [com.google.mlkit:digital-ink-recognition:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8fc52eb327e2202155f3dbc243d8187a\transformed\jetified-digital-ink-recognition-18.1.0\AndroidManifest.xml:2:1-21:12
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:17:1-145:12
MERGED from [com.google.mlkit:text-recognition:16.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\9e4d5ede34cda30aacec2e1441971f96\transformed\jetified-text-recognition-16.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.mlkit:text-recognition-bundled-common:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b3f261dbfc5d19251e9661ae987f692\transformed\jetified-text-recognition-bundled-common-17.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition:19.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\ce37485b62915f36742f98b9c5e96ba9\transformed\jetified-play-services-mlkit-text-recognition-19.0.1\AndroidManifest.xml:2:1-8:12
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition-common:19.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed51e9ea00ee12a4c2d9fc12ca35b3c2\transformed\jetified-play-services-mlkit-text-recognition-common-19.1.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\e62b8171866d636f8ed6a0bfd220b8f2\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:2:1-18:12
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\62aeb653dda202444616439a5b81d504\transformed\jetified-credentials-1.2.0-rc01\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\811d866da11c4adc1e237c7be48ea17a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:17:1-44:12
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3998f03731776e641b0412496162b18\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:17:1-40:12
MERGED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f79562eedfa06deadff6479d4f4acac\transformed\jetified-billing-7.1.1\AndroidManifest.xml:2:1-38:12
MERGED from [com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\a2bc3fa9b88a89bfa05736f8fd4a3121\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:2:1-26:12
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\076acdf74b13b0d0be55167d9cf30c82\transformed\jetified-common-18.11.0\AndroidManifest.xml:2:1-26:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\616b60c58236cf257396375878eb11aa\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\transforms-3\31fb58f11e86d5467d2c7c71d07353b8\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-location:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\946c41ae743610321d53df650d1a1a0d\transformed\jetified-play-services-location-19.0.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\447162408152222549d7f79b7bf5f55b\transformed\jetified-firebase-appcheck-interop-17.0.0\AndroidManifest.xml:15:1-25:12
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\98ea7c20eb2d38e8c9188c73979956a1\transformed\jetified-firebase-database-collection-18.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e42a5d0b6bf6f23c86f61473a141749\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b368049dbdfe7c192fa848707b37b6\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f5a019bffc1f9b189f3d5a06719dd484\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\550b14c5117666deb34b8cf6b1829c33\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3bb114678107da1ef99f97798dc193bb\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a2a94c3d197e9b946fa4f683bc141b6b\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\b9e27e61055dde67f8637b8f89a8f0a0\transformed\jetified-integrity-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\413187884bd07825bbb7564f504ae288\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4595a3b296e32e7039e98543d2f65518\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\596dc2cdab8b9d2ef83e7d6e0e134b8a\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\9c73351b3eef23b386c6d565f7babcb3\transformed\fragment-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\533b9bce64e47fda78cbe1baa0b6ca7d\transformed\jetified-activity-ktx-1.8.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\49f654a543c702b15417705ac1f9d737\transformed\jetified-activity-1.8.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8851592c8a946ad79fa6f49cdb522840\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ef855e7efb5ae25a51573e5605ab6612\transformed\loader-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2319c56b9cfd2a5b60e4b20ec5eb99ae\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c6e989c6c7df602195c3a4bfb2989f60\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\02758c2639608ddeb41a1cc8871e22ca\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7bd6cd68b2dfb6d856e0136c55cf13cf\transformed\jetified-lifecycle-service-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2f13277dadda22a5dec6af8e85ed61f5\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f4e96cff6687fdf30a110a5e18edf8ad\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c82c4d384cf906d14a42b3afe79264c6\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\22bdf8c02212a84b53c5394bd2b9a913\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9da313400a33b9893d3dbfb49fa206ea\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\aac14126756343a762a1dd95f0000a89\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\cb4f9b7546ddd8ec180df336ad808fa4\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\952d74a4f84de990b1d8333e6126b61e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\6b3b87df348d283ed0d0177ddac4a209\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c1d441b924febfe273cac3f5a70e0a3e\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\b6e6fee4870df8ecd684116bf49b75c8\transformed\jetified-datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\a0be6dffea1cae977021334fd59ef10b\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\b5a5938ba4c337d2780ca6b2831f0454\transformed\jetified-datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.mlkit:vision-interfaces:16.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c424c45df9d48c0620d309841946709a\transformed\jetified-vision-interfaces-16.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c7904ef7c3d667722357509ea858f00b\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-places-placereport:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a8b9c038a2e82e8e93ea2f0161633e5c\transformed\jetified-play-services-places-placereport-17.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\19d4c2635369e2aa123936ad1ab3a080\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\f6c9548f82100dc94f1188394ca35efd\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c93ab39decd20c09c6303533cf403128\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\419492b5dae482da85a0150bbdaae4d2\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\42642eb3456df98d1184c1227b27f028\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\16eadfbba7fb02250003a99a859398f1\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\bf696adcbb799744dfabaf7bcafd0c7b\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0be65c6b08cdaacd775b64b1191fa28f\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\32e36d3218dafc29a46c2028c6cc6218\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ec7a269638eb32eeb086ec0887d357be\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f9cd0e3767e1905ae98929b9ac6363ac\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\fe930d2507b0279f955567b6d5748a32\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\transforms-3\60d11028ed788bd90eba3bf1e273bf97\transformed\jetified-security-crypto-1.1.0-alpha06\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\5366afd6a5dea0a6abfe5e8efa6b7106\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\fddaf1edf19f323b6cf7e30f79481aa5\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b740f3eec8188e2f93c4f646edb23e95\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.exifinterface:exifinterface:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fa3bc5b62c3630704ff5037165230ba2\transformed\exifinterface-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\5dc29b04cf00cdbc8783e786639e2a69\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\ee969d6aefaa82cfa2f0e33fc4ab16ac\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a5e41fef5de1e0e60b88e968f586d3bc\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.android.datatransport:transport-api:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\57d579760aca6c65366b4076617d6c21\transformed\jetified-transport-api-3.0.0\AndroidManifest.xml:15:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\597b41660af57d4d3fae8706c9b2ad77\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\e3cf089486f7ad4e20f60677fe72bdf6\transformed\room-runtime-2.2.5\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ae15ae464d3f6a2bffa5ec2c13d9f7f0\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7a56609f2c6db04c69b509fd23d4c78a\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e9b39a41d79ff82bba7ed59a7f23b75c\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\da3329929c45e92bce291c0144c04ede\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d9262a11e3d2483091659e688013802e\transformed\sqlite-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\054c76b58a6a5d9ab83b41630a67375d\transformed\jetified-core-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9db5839c92106ad2fc1c7885a9372c6a\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\432b7103dd38a863b06280610c3f4868\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a17b11aa678369c8678c520130bbcaae\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\9c9b8e3e4f17d010e1c0e832e4e70c3a\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\12a042b35be742b7508d99fb55eb3e0f\transformed\jetified-googleid-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\transforms-3\7d5234e950f5385531974a8f23d7332a\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\transforms-3\3d937d2078c6e546e68ff6956c0cf58b\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:2:1-9:12
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\transforms-3\e871f36828b285701d8f51c5db126acc\transformed\jetified-grpc-android-1.62.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:protolite-well-known-types:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\9181911fb9a4ca5d3b3fb3a0a682da8e\transformed\jetified-protolite-well-known-types-18.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\17add4f136784dea2dd439689278c96a\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:2:1-21:12
	package
		INJECTED from C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\debug\AndroidManifest.xml
	android:versionCode
		INJECTED from C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.CAMERA
ADDED from C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\main\AndroidManifest.xml:3:5-65
MERGED from [:camera_android] C:\Users\<USER>\Documents\environments\troedelpos\build\camera_android\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-65
MERGED from [:camera_android] C:\Users\<USER>\Documents\environments\troedelpos\build\camera_android\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-65
	android:name
		ADDED from C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\main\AndroidManifest.xml:3:22-62
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\main\AndroidManifest.xml:5:5-67
MERGED from C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\main\AndroidManifest.xml:5:5-67
MERGED from C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\main\AndroidManifest.xml:5:5-67
MERGED from [:google_sign_in_android] C:\Users\<USER>\Documents\environments\troedelpos\build\google_sign_in_android\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-67
MERGED from [:google_sign_in_android] C:\Users\<USER>\Documents\environments\troedelpos\build\google_sign_in_android\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-67
MERGED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:25:5-67
MERGED from [com.google.mlkit:digital-ink-recognition:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8fc52eb327e2202155f3dbc243d8187a\transformed\jetified-digital-ink-recognition-18.1.0\AndroidManifest.xml:9:5-67
MERGED from [com.google.mlkit:digital-ink-recognition:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8fc52eb327e2202155f3dbc243d8187a\transformed\jetified-digital-ink-recognition-18.1.0\AndroidManifest.xml:9:5-67
MERGED from [com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\a2bc3fa9b88a89bfa05736f8fd4a3121\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\a2bc3fa9b88a89bfa05736f8fd4a3121\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:11:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a2a94c3d197e9b946fa4f683bc141b6b\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a2a94c3d197e9b946fa4f683bc141b6b\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\5dc29b04cf00cdbc8783e786639e2a69\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\5dc29b04cf00cdbc8783e786639e2a69\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:25:5-67
	android:name
		ADDED from C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\main\AndroidManifest.xml:7:5-81
	android:name
		ADDED from C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\main\AndroidManifest.xml:7:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\main\AndroidManifest.xml:8:5-80
	android:name
		ADDED from C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\main\AndroidManifest.xml:8:22-77
uses-permission#com.android.vending.BILLING
ADDED from C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\main\AndroidManifest.xml:10:5-67
MERGED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f79562eedfa06deadff6479d4f4acac\transformed\jetified-billing-7.1.1\AndroidManifest.xml:10:5-67
MERGED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f79562eedfa06deadff6479d4f4acac\transformed\jetified-billing-7.1.1\AndroidManifest.xml:10:5-67
	android:name
		ADDED from C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\main\AndroidManifest.xml:10:22-64
queries
ADDED from C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\main\AndroidManifest.xml:71:5-76:15
MERGED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f79562eedfa06deadff6479d4f4acac\transformed\jetified-billing-7.1.1\AndroidManifest.xml:12:5-19:15
MERGED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f79562eedfa06deadff6479d4f4acac\transformed\jetified-billing-7.1.1\AndroidManifest.xml:12:5-19:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\main\AndroidManifest.xml:72:9-75:18
action#android.intent.action.PROCESS_TEXT
ADDED from C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\main\AndroidManifest.xml:73:13-72
	android:name
		ADDED from C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\main\AndroidManifest.xml:73:21-70
data
ADDED from C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\main\AndroidManifest.xml:74:13-50
	android:mimeType
		ADDED from C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\main\AndroidManifest.xml:74:19-48
uses-sdk
INJECTED from C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\debug\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\debug\AndroidManifest.xml
MERGED from [:camera_android] C:\Users\<USER>\Documents\environments\troedelpos\build\camera_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:camera_android] C:\Users\<USER>\Documents\environments\troedelpos\build\camera_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:cloud_firestore] C:\Users\<USER>\Documents\environments\troedelpos\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:cloud_firestore] C:\Users\<USER>\Documents\environments\troedelpos\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:connectivity_plus] C:\Users\<USER>\Documents\environments\troedelpos\build\connectivity_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:connectivity_plus] C:\Users\<USER>\Documents\environments\troedelpos\build\connectivity_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:firebase_auth] C:\Users\<USER>\Documents\environments\troedelpos\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:firebase_auth] C:\Users\<USER>\Documents\environments\troedelpos\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] C:\Users\<USER>\Documents\environments\troedelpos\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] C:\Users\<USER>\Documents\environments\troedelpos\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\Documents\environments\troedelpos\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\Documents\environments\troedelpos\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:flutter_secure_storage] C:\Users\<USER>\Documents\environments\troedelpos\build\flutter_secure_storage\intermediates\merged_manifest\debug\AndroidManifest.xml:6:5-8:53
MERGED from [:flutter_secure_storage] C:\Users\<USER>\Documents\environments\troedelpos\build\flutter_secure_storage\intermediates\merged_manifest\debug\AndroidManifest.xml:6:5-8:53
MERGED from [:google_mlkit_digital_ink_recognition] C:\Users\<USER>\Documents\environments\troedelpos\build\google_mlkit_digital_ink_recognition\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:google_mlkit_digital_ink_recognition] C:\Users\<USER>\Documents\environments\troedelpos\build\google_mlkit_digital_ink_recognition\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:google_mlkit_text_recognition] C:\Users\<USER>\Documents\environments\troedelpos\build\google_mlkit_text_recognition\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:google_mlkit_text_recognition] C:\Users\<USER>\Documents\environments\troedelpos\build\google_mlkit_text_recognition\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:google_mlkit_commons] C:\Users\<USER>\Documents\environments\troedelpos\build\google_mlkit_commons\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:google_mlkit_commons] C:\Users\<USER>\Documents\environments\troedelpos\build\google_mlkit_commons\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:google_sign_in_android] C:\Users\<USER>\Documents\environments\troedelpos\build\google_sign_in_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:google_sign_in_android] C:\Users\<USER>\Documents\environments\troedelpos\build\google_sign_in_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:in_app_purchase_android] C:\Users\<USER>\Documents\environments\troedelpos\build\in_app_purchase_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:in_app_purchase_android] C:\Users\<USER>\Documents\environments\troedelpos\build\in_app_purchase_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] C:\Users\<USER>\Documents\environments\troedelpos\build\path_provider_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] C:\Users\<USER>\Documents\environments\troedelpos\build\path_provider_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] C:\Users\<USER>\Documents\environments\troedelpos\build\permission_handler_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] C:\Users\<USER>\Documents\environments\troedelpos\build\permission_handler_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:share_plus] C:\Users\<USER>\Documents\environments\troedelpos\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:share_plus] C:\Users\<USER>\Documents\environments\troedelpos\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] C:\Users\<USER>\Documents\environments\troedelpos\build\shared_preferences_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] C:\Users\<USER>\Documents\environments\troedelpos\build\shared_preferences_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] C:\Users\<USER>\Documents\environments\troedelpos\build\sqflite_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] C:\Users\<USER>\Documents\environments\troedelpos\build\sqflite_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] C:\Users\<USER>\Documents\environments\troedelpos\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] C:\Users\<USER>\Documents\environments\troedelpos\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a75011f7f88673231c312008696729f9\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a75011f7f88673231c312008696729f9\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:21:5-23:151
MERGED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:21:5-23:151
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f6219dea7a69dbc6d2bf4921723a7d5\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f6219dea7a69dbc6d2bf4921723a7d5\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\88a95dc090f8ff91430f7b0681cf077e\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\88a95dc090f8ff91430f7b0681cf077e\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\93e6004bff5aedd63abbe49bb91b469e\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\93e6004bff5aedd63abbe49bb91b469e\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6811d104c5a3aa633077b6f7528fd5be\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6811d104c5a3aa633077b6f7528fd5be\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\69a6d671be6bdd1a1fdb40a1fed71722\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\69a6d671be6bdd1a1fdb40a1fed71722\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\636e11c147967ac5df8d2d450eee6ea7\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\636e11c147967ac5df8d2d450eee6ea7\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.mlkit:digital-ink-recognition:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8fc52eb327e2202155f3dbc243d8187a\transformed\jetified-digital-ink-recognition-18.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:digital-ink-recognition:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8fc52eb327e2202155f3dbc243d8187a\transformed\jetified-digital-ink-recognition-18.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:21:5-23:41
MERGED from [com.google.mlkit:text-recognition:16.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\9e4d5ede34cda30aacec2e1441971f96\transformed\jetified-text-recognition-16.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:text-recognition:16.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\9e4d5ede34cda30aacec2e1441971f96\transformed\jetified-text-recognition-16.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:text-recognition-bundled-common:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b3f261dbfc5d19251e9661ae987f692\transformed\jetified-text-recognition-bundled-common-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:text-recognition-bundled-common:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b3f261dbfc5d19251e9661ae987f692\transformed\jetified-text-recognition-bundled-common-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition:19.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\ce37485b62915f36742f98b9c5e96ba9\transformed\jetified-play-services-mlkit-text-recognition-19.0.1\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition:19.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\ce37485b62915f36742f98b9c5e96ba9\transformed\jetified-play-services-mlkit-text-recognition-19.0.1\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition-common:19.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed51e9ea00ee12a4c2d9fc12ca35b3c2\transformed\jetified-play-services-mlkit-text-recognition-common-19.1.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition-common:19.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed51e9ea00ee12a4c2d9fc12ca35b3c2\transformed\jetified-play-services-mlkit-text-recognition-common-19.1.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\e62b8171866d636f8ed6a0bfd220b8f2\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\e62b8171866d636f8ed6a0bfd220b8f2\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:6:5-44
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\62aeb653dda202444616439a5b81d504\transformed\jetified-credentials-1.2.0-rc01\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\62aeb653dda202444616439a5b81d504\transformed\jetified-credentials-1.2.0-rc01\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\811d866da11c4adc1e237c7be48ea17a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:21:5-44
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\811d866da11c4adc1e237c7be48ea17a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3998f03731776e641b0412496162b18\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3998f03731776e641b0412496162b18\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f79562eedfa06deadff6479d4f4acac\transformed\jetified-billing-7.1.1\AndroidManifest.xml:6:5-8:41
MERGED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f79562eedfa06deadff6479d4f4acac\transformed\jetified-billing-7.1.1\AndroidManifest.xml:6:5-8:41
MERGED from [com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\a2bc3fa9b88a89bfa05736f8fd4a3121\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\a2bc3fa9b88a89bfa05736f8fd4a3121\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\076acdf74b13b0d0be55167d9cf30c82\transformed\jetified-common-18.11.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\076acdf74b13b0d0be55167d9cf30c82\transformed\jetified-common-18.11.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\616b60c58236cf257396375878eb11aa\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\616b60c58236cf257396375878eb11aa\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\transforms-3\31fb58f11e86d5467d2c7c71d07353b8\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\transforms-3\31fb58f11e86d5467d2c7c71d07353b8\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\946c41ae743610321d53df650d1a1a0d\transformed\jetified-play-services-location-19.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\946c41ae743610321d53df650d1a1a0d\transformed\jetified-play-services-location-19.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\447162408152222549d7f79b7bf5f55b\transformed\jetified-firebase-appcheck-interop-17.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\447162408152222549d7f79b7bf5f55b\transformed\jetified-firebase-appcheck-interop-17.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\98ea7c20eb2d38e8c9188c73979956a1\transformed\jetified-firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\98ea7c20eb2d38e8c9188c73979956a1\transformed\jetified-firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e42a5d0b6bf6f23c86f61473a141749\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e42a5d0b6bf6f23c86f61473a141749\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b368049dbdfe7c192fa848707b37b6\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b368049dbdfe7c192fa848707b37b6\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f5a019bffc1f9b189f3d5a06719dd484\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f5a019bffc1f9b189f3d5a06719dd484\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\550b14c5117666deb34b8cf6b1829c33\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\550b14c5117666deb34b8cf6b1829c33\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3bb114678107da1ef99f97798dc193bb\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3bb114678107da1ef99f97798dc193bb\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a2a94c3d197e9b946fa4f683bc141b6b\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a2a94c3d197e9b946fa4f683bc141b6b\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\b9e27e61055dde67f8637b8f89a8f0a0\transformed\jetified-integrity-1.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\b9e27e61055dde67f8637b8f89a8f0a0\transformed\jetified-integrity-1.3.0\AndroidManifest.xml:4:5-44
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\413187884bd07825bbb7564f504ae288\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\413187884bd07825bbb7564f504ae288\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4595a3b296e32e7039e98543d2f65518\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4595a3b296e32e7039e98543d2f65518\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\596dc2cdab8b9d2ef83e7d6e0e134b8a\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\596dc2cdab8b9d2ef83e7d6e0e134b8a\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\9c73351b3eef23b386c6d565f7babcb3\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\9c73351b3eef23b386c6d565f7babcb3\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\533b9bce64e47fda78cbe1baa0b6ca7d\transformed\jetified-activity-ktx-1.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\533b9bce64e47fda78cbe1baa0b6ca7d\transformed\jetified-activity-ktx-1.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\49f654a543c702b15417705ac1f9d737\transformed\jetified-activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\49f654a543c702b15417705ac1f9d737\transformed\jetified-activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8851592c8a946ad79fa6f49cdb522840\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8851592c8a946ad79fa6f49cdb522840\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ef855e7efb5ae25a51573e5605ab6612\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ef855e7efb5ae25a51573e5605ab6612\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2319c56b9cfd2a5b60e4b20ec5eb99ae\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2319c56b9cfd2a5b60e4b20ec5eb99ae\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c6e989c6c7df602195c3a4bfb2989f60\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c6e989c6c7df602195c3a4bfb2989f60\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\02758c2639608ddeb41a1cc8871e22ca\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\02758c2639608ddeb41a1cc8871e22ca\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7bd6cd68b2dfb6d856e0136c55cf13cf\transformed\jetified-lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7bd6cd68b2dfb6d856e0136c55cf13cf\transformed\jetified-lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2f13277dadda22a5dec6af8e85ed61f5\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2f13277dadda22a5dec6af8e85ed61f5\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f4e96cff6687fdf30a110a5e18edf8ad\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f4e96cff6687fdf30a110a5e18edf8ad\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c82c4d384cf906d14a42b3afe79264c6\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c82c4d384cf906d14a42b3afe79264c6\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\22bdf8c02212a84b53c5394bd2b9a913\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\22bdf8c02212a84b53c5394bd2b9a913\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9da313400a33b9893d3dbfb49fa206ea\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9da313400a33b9893d3dbfb49fa206ea\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\aac14126756343a762a1dd95f0000a89\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\aac14126756343a762a1dd95f0000a89\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\cb4f9b7546ddd8ec180df336ad808fa4\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\cb4f9b7546ddd8ec180df336ad808fa4\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\952d74a4f84de990b1d8333e6126b61e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\952d74a4f84de990b1d8333e6126b61e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\6b3b87df348d283ed0d0177ddac4a209\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\6b3b87df348d283ed0d0177ddac4a209\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c1d441b924febfe273cac3f5a70e0a3e\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c1d441b924febfe273cac3f5a70e0a3e\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\b6e6fee4870df8ecd684116bf49b75c8\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\b6e6fee4870df8ecd684116bf49b75c8\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\a0be6dffea1cae977021334fd59ef10b\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\a0be6dffea1cae977021334fd59ef10b\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\b5a5938ba4c337d2780ca6b2831f0454\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\b5a5938ba4c337d2780ca6b2831f0454\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [com.google.mlkit:vision-interfaces:16.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c424c45df9d48c0620d309841946709a\transformed\jetified-vision-interfaces-16.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-interfaces:16.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c424c45df9d48c0620d309841946709a\transformed\jetified-vision-interfaces-16.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c7904ef7c3d667722357509ea858f00b\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c7904ef7c3d667722357509ea858f00b\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-places-placereport:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a8b9c038a2e82e8e93ea2f0161633e5c\transformed\jetified-play-services-places-placereport-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-places-placereport:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a8b9c038a2e82e8e93ea2f0161633e5c\transformed\jetified-play-services-places-placereport-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\19d4c2635369e2aa123936ad1ab3a080\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\19d4c2635369e2aa123936ad1ab3a080\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\f6c9548f82100dc94f1188394ca35efd\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\f6c9548f82100dc94f1188394ca35efd\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c93ab39decd20c09c6303533cf403128\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c93ab39decd20c09c6303533cf403128\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\419492b5dae482da85a0150bbdaae4d2\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\419492b5dae482da85a0150bbdaae4d2\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\42642eb3456df98d1184c1227b27f028\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\42642eb3456df98d1184c1227b27f028\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\16eadfbba7fb02250003a99a859398f1\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\16eadfbba7fb02250003a99a859398f1\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\bf696adcbb799744dfabaf7bcafd0c7b\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\bf696adcbb799744dfabaf7bcafd0c7b\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0be65c6b08cdaacd775b64b1191fa28f\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0be65c6b08cdaacd775b64b1191fa28f\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\32e36d3218dafc29a46c2028c6cc6218\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\32e36d3218dafc29a46c2028c6cc6218\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ec7a269638eb32eeb086ec0887d357be\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ec7a269638eb32eeb086ec0887d357be\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f9cd0e3767e1905ae98929b9ac6363ac\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f9cd0e3767e1905ae98929b9ac6363ac\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\fe930d2507b0279f955567b6d5748a32\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\fe930d2507b0279f955567b6d5748a32\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\transforms-3\60d11028ed788bd90eba3bf1e273bf97\transformed\jetified-security-crypto-1.1.0-alpha06\AndroidManifest.xml:20:5-44
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\transforms-3\60d11028ed788bd90eba3bf1e273bf97\transformed\jetified-security-crypto-1.1.0-alpha06\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\5366afd6a5dea0a6abfe5e8efa6b7106\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\5366afd6a5dea0a6abfe5e8efa6b7106\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\fddaf1edf19f323b6cf7e30f79481aa5\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\fddaf1edf19f323b6cf7e30f79481aa5\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b740f3eec8188e2f93c4f646edb23e95\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b740f3eec8188e2f93c4f646edb23e95\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fa3bc5b62c3630704ff5037165230ba2\transformed\exifinterface-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fa3bc5b62c3630704ff5037165230ba2\transformed\exifinterface-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\5dc29b04cf00cdbc8783e786639e2a69\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\5dc29b04cf00cdbc8783e786639e2a69\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\ee969d6aefaa82cfa2f0e33fc4ab16ac\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\ee969d6aefaa82cfa2f0e33fc4ab16ac\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a5e41fef5de1e0e60b88e968f586d3bc\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a5e41fef5de1e0e60b88e968f586d3bc\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-api:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\57d579760aca6c65366b4076617d6c21\transformed\jetified-transport-api-3.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\57d579760aca6c65366b4076617d6c21\transformed\jetified-transport-api-3.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\597b41660af57d4d3fae8706c9b2ad77\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\597b41660af57d4d3fae8706c9b2ad77\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\e3cf089486f7ad4e20f60677fe72bdf6\transformed\room-runtime-2.2.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\e3cf089486f7ad4e20f60677fe72bdf6\transformed\room-runtime-2.2.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ae15ae464d3f6a2bffa5ec2c13d9f7f0\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ae15ae464d3f6a2bffa5ec2c13d9f7f0\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7a56609f2c6db04c69b509fd23d4c78a\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7a56609f2c6db04c69b509fd23d4c78a\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e9b39a41d79ff82bba7ed59a7f23b75c\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e9b39a41d79ff82bba7ed59a7f23b75c\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\da3329929c45e92bce291c0144c04ede\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\da3329929c45e92bce291c0144c04ede\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d9262a11e3d2483091659e688013802e\transformed\sqlite-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d9262a11e3d2483091659e688013802e\transformed\sqlite-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\054c76b58a6a5d9ab83b41630a67375d\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\054c76b58a6a5d9ab83b41630a67375d\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9db5839c92106ad2fc1c7885a9372c6a\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9db5839c92106ad2fc1c7885a9372c6a\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\432b7103dd38a863b06280610c3f4868\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\432b7103dd38a863b06280610c3f4868\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a17b11aa678369c8678c520130bbcaae\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a17b11aa678369c8678c520130bbcaae\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\9c9b8e3e4f17d010e1c0e832e4e70c3a\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\9c9b8e3e4f17d010e1c0e832e4e70c3a\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\12a042b35be742b7508d99fb55eb3e0f\transformed\jetified-googleid-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\12a042b35be742b7508d99fb55eb3e0f\transformed\jetified-googleid-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\transforms-3\7d5234e950f5385531974a8f23d7332a\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\transforms-3\7d5234e950f5385531974a8f23d7332a\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\transforms-3\3d937d2078c6e546e68ff6956c0cf58b\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\transforms-3\3d937d2078c6e546e68ff6956c0cf58b\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:5:5-44
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\transforms-3\e871f36828b285701d8f51c5db126acc\transformed\jetified-grpc-android-1.62.2\AndroidManifest.xml:5:5-44
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\transforms-3\e871f36828b285701d8f51c5db126acc\transformed\jetified-grpc-android-1.62.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:protolite-well-known-types:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\9181911fb9a4ca5d3b3fb3a0a682da8e\transformed\jetified-protolite-well-known-types-18.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:protolite-well-known-types:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\9181911fb9a4ca5d3b3fb3a0a682da8e\transformed\jetified-protolite-well-known-types-18.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\17add4f136784dea2dd439689278c96a\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\17add4f136784dea2dd439689278c96a\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:7:5-9:41
	tools:overrideLibrary
		ADDED from [:flutter_secure_storage] C:\Users\<USER>\Documents\environments\troedelpos\build\flutter_secure_storage\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-50
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\debug\AndroidManifest.xml
uses-permission#android.permission.RECORD_AUDIO
ADDED from [:camera_android] C:\Users\<USER>\Documents\environments\troedelpos\build\camera_android\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-71
	android:name
		ADDED from [:camera_android] C:\Users\<USER>\Documents\environments\troedelpos\build\camera_android\intermediates\merged_manifest\debug\AndroidManifest.xml:8:22-68
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [:cloud_firestore] C:\Users\<USER>\Documents\environments\troedelpos\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_auth] C:\Users\<USER>\Documents\environments\troedelpos\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_auth] C:\Users\<USER>\Documents\environments\troedelpos\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_core] C:\Users\<USER>\Documents\environments\troedelpos\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_core] C:\Users\<USER>\Documents\environments\troedelpos\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:19
MERGED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\a2bc3fa9b88a89bfa05736f8fd4a3121\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\a2bc3fa9b88a89bfa05736f8fd4a3121\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\550b14c5117666deb34b8cf6b1829c33\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\550b14c5117666deb34b8cf6b1829c33\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3bb114678107da1ef99f97798dc193bb\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3bb114678107da1ef99f97798dc193bb\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:68:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3bb114678107da1ef99f97798dc193bb\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3bb114678107da1ef99f97798dc193bb\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [:cloud_firestore] C:\Users\<USER>\Documents\environments\troedelpos\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:8:18-89
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.firestore.FlutterFirebaseFirestoreRegistrar
ADDED from [:cloud_firestore] C:\Users\<USER>\Documents\environments\troedelpos\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:cloud_firestore] C:\Users\<USER>\Documents\environments\troedelpos\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:cloud_firestore] C:\Users\<USER>\Documents\environments\troedelpos\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-134
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [:connectivity_plus] C:\Users\<USER>\Documents\environments\troedelpos\build\connectivity_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.mlkit:digital-ink-recognition:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8fc52eb327e2202155f3dbc243d8187a\transformed\jetified-digital-ink-recognition-18.1.0\AndroidManifest.xml:8:5-79
MERGED from [com.google.mlkit:digital-ink-recognition:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8fc52eb327e2202155f3dbc243d8187a\transformed\jetified-digital-ink-recognition-18.1.0\AndroidManifest.xml:8:5-79
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:26:5-79
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\a2bc3fa9b88a89bfa05736f8fd4a3121\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:10:5-79
MERGED from [com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\a2bc3fa9b88a89bfa05736f8fd4a3121\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:10:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a2a94c3d197e9b946fa4f683bc141b6b\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a2a94c3d197e9b946fa4f683bc141b6b\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\5dc29b04cf00cdbc8783e786639e2a69\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\5dc29b04cf00cdbc8783e786639e2a69\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\ee969d6aefaa82cfa2f0e33fc4ab16ac\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\ee969d6aefaa82cfa2f0e33fc4ab16ac\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:22:5-79
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\transforms-3\e871f36828b285701d8f51c5db126acc\transformed\jetified-grpc-android-1.62.2\AndroidManifest.xml:7:5-79
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\transforms-3\e871f36828b285701d8f51c5db126acc\transformed\jetified-grpc-android-1.62.2\AndroidManifest.xml:7:5-79
	android:name
		ADDED from [:connectivity_plus] C:\Users\<USER>\Documents\environments\troedelpos\build\connectivity_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:7:22-76
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar
ADDED from [:firebase_auth] C:\Users\<USER>\Documents\environments\troedelpos\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_auth] C:\Users\<USER>\Documents\environments\troedelpos\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_auth] C:\Users\<USER>\Documents\environments\troedelpos\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-124
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar
ADDED from [:firebase_core] C:\Users\<USER>\Documents\environments\troedelpos\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_core] C:\Users\<USER>\Documents\environments\troedelpos\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_core] C:\Users\<USER>\Documents\environments\troedelpos\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-124
provider#dev.fluttercommunity.plus.share.ShareFileProvider
ADDED from [:share_plus] C:\Users\<USER>\Documents\environments\troedelpos\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:13:9-21:20
	android:grantUriPermissions
		ADDED from [:share_plus] C:\Users\<USER>\Documents\environments\troedelpos\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:17:13-47
	android:authorities
		ADDED from [:share_plus] C:\Users\<USER>\Documents\environments\troedelpos\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:15:13-74
	android:exported
		ADDED from [:share_plus] C:\Users\<USER>\Documents\environments\troedelpos\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:16:13-37
	android:name
		ADDED from [:share_plus] C:\Users\<USER>\Documents\environments\troedelpos\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-77
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:share_plus] C:\Users\<USER>\Documents\environments\troedelpos\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:18:13-20:68
	android:resource
		ADDED from [:share_plus] C:\Users\<USER>\Documents\environments\troedelpos\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:20:17-65
	android:name
		ADDED from [:share_plus] C:\Users\<USER>\Documents\environments\troedelpos\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:19:17-67
receiver#dev.fluttercommunity.plus.share.SharePlusPendingIntent
ADDED from [:share_plus] C:\Users\<USER>\Documents\environments\troedelpos\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:26:9-32:20
	android:exported
		ADDED from [:share_plus] C:\Users\<USER>\Documents\environments\troedelpos\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [:share_plus] C:\Users\<USER>\Documents\environments\troedelpos\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:27:13-82
intent-filter#action:name:EXTRA_CHOSEN_COMPONENT
ADDED from [:share_plus] C:\Users\<USER>\Documents\environments\troedelpos\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:29:13-31:29
action#EXTRA_CHOSEN_COMPONENT
ADDED from [:share_plus] C:\Users\<USER>\Documents\environments\troedelpos\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:30:17-65
	android:name
		ADDED from [:share_plus] C:\Users\<USER>\Documents\environments\troedelpos\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:30:25-62
activity#io.flutter.plugins.urllauncher.WebViewActivity
ADDED from [:url_launcher_android] C:\Users\<USER>\Documents\environments\troedelpos\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-11:74
	android:exported
		ADDED from [:url_launcher_android] C:\Users\<USER>\Documents\environments\troedelpos\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [:url_launcher_android] C:\Users\<USER>\Documents\environments\troedelpos\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-71
	android:name
		ADDED from [:url_launcher_android] C:\Users\<USER>\Documents\environments\troedelpos\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-74
activity#com.google.firebase.auth.internal.GenericIdpActivity
ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:29:9-46:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:31:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:33:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:32:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:34:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:30:13-80
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:genericidp
ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:35:13-45:29
action#android.intent.action.VIEW
ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:36:17-69
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:36:25-66
category#android.intent.category.DEFAULT
ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:38:17-76
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:38:27-73
category#android.intent.category.BROWSABLE
ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:39:17-78
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:39:27-75
activity#com.google.firebase.auth.internal.RecaptchaActivity
ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:47:9-64:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:49:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:51:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:50:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:52:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:48:13-79
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:recaptcha
ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:53:13-63:29
meta-data#com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar
ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:69:13-71:85
	android:value
		ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:71:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:70:17-109
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\69a6d671be6bdd1a1fdb40a1fed71722\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\69a6d671be6bdd1a1fdb40a1fed71722\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\69a6d671be6bdd1a1fdb40a1fed71722\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\69a6d671be6bdd1a1fdb40a1fed71722\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\69a6d671be6bdd1a1fdb40a1fed71722\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\69a6d671be6bdd1a1fdb40a1fed71722\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
service#com.google.mlkit.common.internal.MlKitComponentDiscoveryService
ADDED from [com.google.mlkit:digital-ink-recognition:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8fc52eb327e2202155f3dbc243d8187a\transformed\jetified-digital-ink-recognition-18.1.0\AndroidManifest.xml:12:9-18:19
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition-common:19.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed51e9ea00ee12a4c2d9fc12ca35b3c2\transformed\jetified-play-services-mlkit-text-recognition-common-19.1.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition-common:19.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed51e9ea00ee12a4c2d9fc12ca35b3c2\transformed\jetified-play-services-mlkit-text-recognition-common-19.1.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\e62b8171866d636f8ed6a0bfd220b8f2\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\e62b8171866d636f8ed6a0bfd220b8f2\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\076acdf74b13b0d0be55167d9cf30c82\transformed\jetified-common-18.11.0\AndroidManifest.xml:15:9-23:19
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\076acdf74b13b0d0be55167d9cf30c82\transformed\jetified-common-18.11.0\AndroidManifest.xml:15:9-23:19
	android:exported
		ADDED from [com.google.mlkit:digital-ink-recognition:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8fc52eb327e2202155f3dbc243d8187a\transformed\jetified-digital-ink-recognition-18.1.0\AndroidManifest.xml:14:13-37
	tools:targetApi
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\076acdf74b13b0d0be55167d9cf30c82\transformed\jetified-common-18.11.0\AndroidManifest.xml:19:13-32
	android:directBootAware
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\076acdf74b13b0d0be55167d9cf30c82\transformed\jetified-common-18.11.0\AndroidManifest.xml:17:13-43
	android:name
		ADDED from [com.google.mlkit:digital-ink-recognition:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8fc52eb327e2202155f3dbc243d8187a\transformed\jetified-digital-ink-recognition-18.1.0\AndroidManifest.xml:13:13-91
meta-data#com.google.firebase.components:com.google.mlkit.vision.digitalink.internal.DigitalInkRecognitionRegistrar
ADDED from [com.google.mlkit:digital-ink-recognition:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8fc52eb327e2202155f3dbc243d8187a\transformed\jetified-digital-ink-recognition-18.1.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.mlkit:digital-ink-recognition:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8fc52eb327e2202155f3dbc243d8187a\transformed\jetified-digital-ink-recognition-18.1.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.mlkit:digital-ink-recognition:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8fc52eb327e2202155f3dbc243d8187a\transformed\jetified-digital-ink-recognition-18.1.0\AndroidManifest.xml:16:17-137
uses-permission#android.permission.WAKE_LOCK
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:25:5-68
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:25:22-65
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:27:5-81
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:27:22-78
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:22-74
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:31:9-39:20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\cb4f9b7546ddd8ec180df336ad808fa4\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\cb4f9b7546ddd8ec180df336ad808fa4\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\952d74a4f84de990b1d8333e6126b61e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\952d74a4f84de990b1d8333e6126b61e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\5366afd6a5dea0a6abfe5e8efa6b7106\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\5366afd6a5dea0a6abfe5e8efa6b7106\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:35:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:33:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:32:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
	android:value
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:46:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:53:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
meta-data#com.google.firebase.components:com.google.mlkit.vision.text.internal.TextRegistrar
ADDED from [com.google.android.gms:play-services-mlkit-text-recognition-common:19.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed51e9ea00ee12a4c2d9fc12ca35b3c2\transformed\jetified-play-services-mlkit-text-recognition-common-19.1.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.android.gms:play-services-mlkit-text-recognition-common:19.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed51e9ea00ee12a4c2d9fc12ca35b3c2\transformed\jetified-play-services-mlkit-text-recognition-common-19.1.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-text-recognition-common:19.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed51e9ea00ee12a4c2d9fc12ca35b3c2\transformed\jetified-play-services-mlkit-text-recognition-common-19.1.0\AndroidManifest.xml:13:17-114
meta-data#com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar
ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\e62b8171866d636f8ed6a0bfd220b8f2\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\e62b8171866d636f8ed6a0bfd220b8f2\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\e62b8171866d636f8ed6a0bfd220b8f2\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:13:17-124
service#androidx.credentials.playservices.CredentialProviderMetadataHolder
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\811d866da11c4adc1e237c7be48ea17a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\811d866da11c4adc1e237c7be48ea17a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\811d866da11c4adc1e237c7be48ea17a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\811d866da11c4adc1e237c7be48ea17a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:28:13-60
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\811d866da11c4adc1e237c7be48ea17a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
meta-data#androidx.credentials.CREDENTIAL_PROVIDER_KEY
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\811d866da11c4adc1e237c7be48ea17a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
	android:value
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\811d866da11c4adc1e237c7be48ea17a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\811d866da11c4adc1e237c7be48ea17a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
activity#androidx.credentials.playservices.HiddenActivity
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\811d866da11c4adc1e237c7be48ea17a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
	android:fitsSystemWindows
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\811d866da11c4adc1e237c7be48ea17a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\811d866da11c4adc1e237c7be48ea17a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\811d866da11c4adc1e237c7be48ea17a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
	android:configChanges
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\811d866da11c4adc1e237c7be48ea17a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
	android:theme
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\811d866da11c4adc1e237c7be48ea17a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\811d866da11c4adc1e237c7be48ea17a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3998f03731776e641b0412496162b18\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3998f03731776e641b0412496162b18\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3998f03731776e641b0412496162b18\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3998f03731776e641b0412496162b18\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3998f03731776e641b0412496162b18\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3998f03731776e641b0412496162b18\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3998f03731776e641b0412496162b18\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3998f03731776e641b0412496162b18\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3998f03731776e641b0412496162b18\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3998f03731776e641b0412496162b18\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
intent#action:name:com.android.vending.billing.InAppBillingService.BIND
ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f79562eedfa06deadff6479d4f4acac\transformed\jetified-billing-7.1.1\AndroidManifest.xml:13:9-15:18
action#com.android.vending.billing.InAppBillingService.BIND
ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f79562eedfa06deadff6479d4f4acac\transformed\jetified-billing-7.1.1\AndroidManifest.xml:14:13-91
	android:name
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f79562eedfa06deadff6479d4f4acac\transformed\jetified-billing-7.1.1\AndroidManifest.xml:14:21-88
intent#action:name:com.google.android.apps.play.billingtestcompanion.BillingOverrideService.BIND
ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f79562eedfa06deadff6479d4f4acac\transformed\jetified-billing-7.1.1\AndroidManifest.xml:16:9-18:18
action#com.google.android.apps.play.billingtestcompanion.BillingOverrideService.BIND
ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f79562eedfa06deadff6479d4f4acac\transformed\jetified-billing-7.1.1\AndroidManifest.xml:17:13-116
	android:name
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f79562eedfa06deadff6479d4f4acac\transformed\jetified-billing-7.1.1\AndroidManifest.xml:17:21-113
meta-data#com.google.android.play.billingclient.version
ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f79562eedfa06deadff6479d4f4acac\transformed\jetified-billing-7.1.1\AndroidManifest.xml:22:9-24:37
	android:value
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f79562eedfa06deadff6479d4f4acac\transformed\jetified-billing-7.1.1\AndroidManifest.xml:24:13-34
	android:name
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f79562eedfa06deadff6479d4f4acac\transformed\jetified-billing-7.1.1\AndroidManifest.xml:23:13-73
activity#com.android.billingclient.api.ProxyBillingActivity
ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f79562eedfa06deadff6479d4f4acac\transformed\jetified-billing-7.1.1\AndroidManifest.xml:26:9-30:75
	android:exported
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f79562eedfa06deadff6479d4f4acac\transformed\jetified-billing-7.1.1\AndroidManifest.xml:29:13-37
	android:configChanges
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f79562eedfa06deadff6479d4f4acac\transformed\jetified-billing-7.1.1\AndroidManifest.xml:28:13-96
	android:theme
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f79562eedfa06deadff6479d4f4acac\transformed\jetified-billing-7.1.1\AndroidManifest.xml:30:13-72
	android:name
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f79562eedfa06deadff6479d4f4acac\transformed\jetified-billing-7.1.1\AndroidManifest.xml:27:13-78
activity#com.android.billingclient.api.ProxyBillingActivityV2
ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f79562eedfa06deadff6479d4f4acac\transformed\jetified-billing-7.1.1\AndroidManifest.xml:31:9-35:75
	android:exported
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f79562eedfa06deadff6479d4f4acac\transformed\jetified-billing-7.1.1\AndroidManifest.xml:34:13-37
	android:configChanges
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f79562eedfa06deadff6479d4f4acac\transformed\jetified-billing-7.1.1\AndroidManifest.xml:33:13-96
	android:theme
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f79562eedfa06deadff6479d4f4acac\transformed\jetified-billing-7.1.1\AndroidManifest.xml:35:13-72
	android:name
		ADDED from [com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f79562eedfa06deadff6479d4f4acac\transformed\jetified-billing-7.1.1\AndroidManifest.xml:32:13-80
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar
ADDED from [com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\a2bc3fa9b88a89bfa05736f8fd4a3121\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:17:13-19:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\a2bc3fa9b88a89bfa05736f8fd4a3121\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:19:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\a2bc3fa9b88a89bfa05736f8fd4a3121\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:18:17-122
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar
ADDED from [com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\a2bc3fa9b88a89bfa05736f8fd4a3121\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\a2bc3fa9b88a89bfa05736f8fd4a3121\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\a2bc3fa9b88a89bfa05736f8fd4a3121\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:21:17-111
provider#com.google.mlkit.common.internal.MlKitInitProvider
ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\076acdf74b13b0d0be55167d9cf30c82\transformed\jetified-common-18.11.0\AndroidManifest.xml:9:9-13:38
	android:authorities
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\076acdf74b13b0d0be55167d9cf30c82\transformed\jetified-common-18.11.0\AndroidManifest.xml:11:13-69
	android:exported
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\076acdf74b13b0d0be55167d9cf30c82\transformed\jetified-common-18.11.0\AndroidManifest.xml:12:13-37
	android:initOrder
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\076acdf74b13b0d0be55167d9cf30c82\transformed\jetified-common-18.11.0\AndroidManifest.xml:13:13-35
	android:name
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\076acdf74b13b0d0be55167d9cf30c82\transformed\jetified-common-18.11.0\AndroidManifest.xml:10:13-78
meta-data#com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar
ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\076acdf74b13b0d0be55167d9cf30c82\transformed\jetified-common-18.11.0\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\076acdf74b13b0d0be55167d9cf30c82\transformed\jetified-common-18.11.0\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\076acdf74b13b0d0be55167d9cf30c82\transformed\jetified-common-18.11.0\AndroidManifest.xml:21:17-120
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b368049dbdfe7c192fa848707b37b6\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b368049dbdfe7c192fa848707b37b6\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b368049dbdfe7c192fa848707b37b6\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b368049dbdfe7c192fa848707b37b6\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\550b14c5117666deb34b8cf6b1829c33\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\550b14c5117666deb34b8cf6b1829c33\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\550b14c5117666deb34b8cf6b1829c33\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3bb114678107da1ef99f97798dc193bb\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3bb114678107da1ef99f97798dc193bb\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3bb114678107da1ef99f97798dc193bb\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3bb114678107da1ef99f97798dc193bb\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3bb114678107da1ef99f97798dc193bb\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3bb114678107da1ef99f97798dc193bb\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3bb114678107da1ef99f97798dc193bb\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3bb114678107da1ef99f97798dc193bb\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3bb114678107da1ef99f97798dc193bb\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
uses-permission#com.google.android.providers.gsf.permission.READ_GSERVICES
ADDED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a2a94c3d197e9b946fa4f683bc141b6b\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:5-98
	android:name
		ADDED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a2a94c3d197e9b946fa4f683bc141b6b\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:22-95
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\cb4f9b7546ddd8ec180df336ad808fa4\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\cb4f9b7546ddd8ec180df336ad808fa4\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\cb4f9b7546ddd8ec180df336ad808fa4\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\952d74a4f84de990b1d8333e6126b61e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\952d74a4f84de990b1d8333e6126b61e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\952d74a4f84de990b1d8333e6126b61e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
meta-data#com.google.android.gms.version
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\19d4c2635369e2aa123936ad1ab3a080\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\19d4c2635369e2aa123936ad1ab3a080\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\fe930d2507b0279f955567b6d5748a32\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\fe930d2507b0279f955567b6d5748a32\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\fe930d2507b0279f955567b6d5748a32\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.example.troedelpos.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\fe930d2507b0279f955567b6d5748a32\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\fe930d2507b0279f955567b6d5748a32\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\fe930d2507b0279f955567b6d5748a32\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\fe930d2507b0279f955567b6d5748a32\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\fe930d2507b0279f955567b6d5748a32\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.example.troedelpos.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\fe930d2507b0279f955567b6d5748a32\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\fe930d2507b0279f955567b6d5748a32\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\5dc29b04cf00cdbc8783e786639e2a69\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\ee969d6aefaa82cfa2f0e33fc4ab16ac\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\ee969d6aefaa82cfa2f0e33fc4ab16ac\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\5dc29b04cf00cdbc8783e786639e2a69\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\5dc29b04cf00cdbc8783e786639e2a69\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\5dc29b04cf00cdbc8783e786639e2a69\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\5dc29b04cf00cdbc8783e786639e2a69\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\5dc29b04cf00cdbc8783e786639e2a69\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\ee969d6aefaa82cfa2f0e33fc4ab16ac\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\ee969d6aefaa82cfa2f0e33fc4ab16ac\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\ee969d6aefaa82cfa2f0e33fc4ab16ac\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\ee969d6aefaa82cfa2f0e33fc4ab16ac\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\ee969d6aefaa82cfa2f0e33fc4ab16ac\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\ee969d6aefaa82cfa2f0e33fc4ab16ac\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\ee969d6aefaa82cfa2f0e33fc4ab16ac\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\e3cf089486f7ad4e20f60677fe72bdf6\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
	android:exported
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\e3cf089486f7ad4e20f60677fe72bdf6\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\e3cf089486f7ad4e20f60677fe72bdf6\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\e3cf089486f7ad4e20f60677fe72bdf6\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
activity#com.google.android.play.core.common.PlayCoreDialogWrapperActivity
ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\17add4f136784dea2dd439689278c96a\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:14:9-18:65
	android:stateNotNeeded
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\17add4f136784dea2dd439689278c96a\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:17:13-42
	android:exported
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\17add4f136784dea2dd439689278c96a\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:16:13-37
	android:theme
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\17add4f136784dea2dd439689278c96a\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:18:13-62
	android:name
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\17add4f136784dea2dd439689278c96a\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:15:13-93
