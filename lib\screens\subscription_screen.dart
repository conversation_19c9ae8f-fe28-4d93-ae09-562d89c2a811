import 'package:flutter/material.dart';
import '../models/subscription_plan.dart';
import '../services/device_service.dart';
import '../services/device_service_extension.dart';
import '../services/subscription_service.dart';

class SubscriptionScreen extends StatefulWidget {
  const SubscriptionScreen({Key? key}) : super(key: key);

  @override
  _SubscriptionScreenState createState() => _SubscriptionScreenState();
}

class _SubscriptionScreenState extends State<SubscriptionScreen> {
  final DeviceService _deviceService = DeviceService();
  final SubscriptionService _subscriptionService = SubscriptionService();
  late SubscriptionType _currentSubscriptionType;
  int _activeDevicesCount = 0;
  bool _isLoading = true;
  
  @override
  void initState() {
    super.initState();
    _loadData();
    
    // Stream für Abonnement-Updates abonnieren
    _subscriptionService.subscriptionStream.listen((_) {
      if (mounted) {
        _loadData();
      }
    });
  }
  
  Future<void> _loadData() async {
    if (!mounted) return;
    
    setState(() {
      _isLoading = true;
    });
    
    try {
      // Initialisierung des SubscriptionService sicherstellen
      await _deviceService.initializeSubscription();
      
      // Aktuelles Abonnement laden - direkt aus dem SubscriptionService
      final currentPlan = _deviceService.getCurrentSubscription();
      _currentSubscriptionType = currentPlan.type;
      
      debugPrint('SubscriptionScreen: Aktuelles Abo geladen: ${currentPlan.type.displayName}');
      
      // Anzahl aktiver Geräte laden
      _activeDevicesCount = await _deviceService.getActiveDevicesCount();
    } catch (e) {
      debugPrint('SubscriptionScreen: Fehler beim Laden der Daten: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
  
  Future<void> _purchaseSubscription(SubscriptionType type) async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      final success = await _deviceService.purchaseSubscription(type);
      
      if (success) {
        // Nach erfolgreichem Kauf Daten neu laden
        await _loadData();
        
        if (!mounted) return;
        
        // Erfolgsmeldung anzeigen
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Abonnement erfolgreich abgeschlossen!'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        if (!mounted) return;
        
        // Fehlermeldung anzeigen
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Der Kauf konnte nicht abgeschlossen werden.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      debugPrint('SubscriptionScreen: Fehler beim Kauf des Abonnements: $e');
      
      if (!mounted) return;
      
      // Fehlermeldung anzeigen
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Fehler: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: Text('Abonnement verwalten'),
      ),
      body: _isLoading
          ? Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Verfügbare Abonnements
                  Text(
                    'Verfügbare Abonnements',
                    style: theme.textTheme.headlineSmall,
                  ),
                  
                  SizedBox(height: 16.0),
                  
                  // Liste der Abonnements
                  ..._buildSubscriptionCards(theme),
                  
                  // Debug-Helfer wurde entfernt
                ],
              ),
            ),
    );
  }
  
  Widget _buildCurrentSubscriptionCard(ThemeData theme) {
    final plan = _deviceService.getCurrentSubscription();
    
    return Card(
      elevation: 4.0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
        side: BorderSide(
          color: theme.colorScheme.primary,
          width: 2.0,
        ),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Aktuelles Abonnement',
                  style: theme.textTheme.titleLarge,
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 12.0, vertical: 6.0),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary,
                    borderRadius: BorderRadius.circular(20.0),
                  ),
                  child: Text(
                    'Aktiv',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 16.0),
            
            Text(
              plan.type.displayName,
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            
            SizedBox(height: 8.0),
            
            Text(
              plan.type.description,
              style: theme.textTheme.bodyLarge,
            ),
            
            SizedBox(height: 16.0),
            
            Row(
              children: [
                Icon(Icons.devices, color: theme.colorScheme.primary),
                SizedBox(width: 8.0),
                Text(
                  '$_activeDevicesCount von ${plan.maxDevices} Geräten aktiv',
                  style: theme.textTheme.bodyLarge,
                ),
              ],
            ),
            
            if (plan.expiryDate != null) ...[
              SizedBox(height: 8.0),
              Row(
                children: [
                  Icon(Icons.calendar_today, color: theme.colorScheme.primary),
                  SizedBox(width: 8.0),
                  Text(
                    'Gültig bis: ${_formatDate(plan.expiryDate!)}',
                    style: theme.textTheme.bodyLarge,
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }
  
  List<Widget> _buildSubscriptionCards(ThemeData theme) {
    final allTypes = _deviceService.getAllSubscriptionTypes();
    
    return allTypes.map((type) {
      final isCurrentType = type == _currentSubscriptionType;
      
      return Card(
        elevation: 2.0,
        margin: EdgeInsets.only(bottom: 16.0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.0),
          side: BorderSide(
            color: isCurrentType ? theme.colorScheme.primary : Colors.transparent,
            width: isCurrentType ? 2.0 : 0.0,
          ),
        ),
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    type.displayName,
                    style: theme.textTheme.titleLarge,
                  ),
                  Text(
                    type.priceDisplay,
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: theme.colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              
              SizedBox(height: 8.0),
              
              Text(
                type.description,
                style: theme.textTheme.bodyLarge,
              ),
              
              SizedBox(height: 16.0),
              
              Row(
                children: [
                  Icon(Icons.devices, color: theme.colorScheme.primary),
                  SizedBox(width: 8.0),
                  Text(
                    '${type.maxDevices} Geräte maximal',
                    style: theme.textTheme.bodyLarge,
                  ),
                ],
              ),
              
              SizedBox(height: 16.0),
              
              if (isCurrentType)
                ElevatedButton.icon(
                  onPressed: null,
                  icon: Icon(Icons.check),
                  label: Text('Aktuelles Abonnement'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey.shade300,
                    foregroundColor: Colors.black54,
                    minimumSize: Size(double.infinity, 48.0),
                  ),
                )
              else
                ElevatedButton.icon(
                  onPressed: () => _purchaseSubscription(type),
                  icon: Icon(Icons.shopping_cart),
                  label: Text(type == SubscriptionType.free
                      ? 'Kostenlos starten'
                      : 'Jetzt abonnieren'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: theme.colorScheme.primary,
                    foregroundColor: Colors.white,
                    minimumSize: Size(double.infinity, 48.0),
                  ),
                ),
            ],
          ),
        ),
      );
    }).toList();
  }
  
  String _formatDate(DateTime date) {
    return '${date.day}.${date.month}.${date.year}';
  }
}
