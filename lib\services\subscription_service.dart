import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart' as fb_auth;
import '../models/subscription_plan.dart';

/// Service zur Verwaltung der Abonnements und In-App-Käufe
class SubscriptionService {
  static final SubscriptionService _instance = SubscriptionService._internal();

  factory SubscriptionService() {
    return _instance;
  }

  SubscriptionService._internal();

  // Singleton-Instanz des InAppPurchase
  final InAppPurchase _inAppPurchase = InAppPurchase.instance;

  // Streams
  final StreamController<SubscriptionPlan> _subscriptionStreamController =
      StreamController<SubscriptionPlan>.broadcast();
  StreamSubscription<List<PurchaseDetails>>? _purchaseStreamSubscription;

  // Aktuelle Produktliste
  List<ProductDetails> _products = [];

  // Firestore-Referenz für Kundenabonnements
  final CollectionReference _subscriptionsCollection =
      FirebaseFirestore.instance.collection('subscriptions');

  // Aktueller Abonnement-Plan
  SubscriptionPlan? _currentPlan;
  String? _customerId;
  bool _isInitialized = false;
  bool _isTestAccount = false;

  /// Getter für den Stream mit Abo-Updates
  Stream<SubscriptionPlan> get subscriptionStream =>
      _subscriptionStreamController.stream;

  /// Getter für verfügbare Produkte
  List<ProductDetails> get availableProducts => _products;

  /// Gibt zurück, ob der Service initialisiert wurde
  bool get isInitialized => _isInitialized;

  /// Initialisiert den Abonnement-Service
  Future<void> initialize(String customerId) async {
    if (_isInitialized) return;

    _customerId = customerId;

    // Prüfen, ob es sich um einen Test-Account handelt
    _isTestAccount = await _checkIfTestAccount(customerId);

    if (_isTestAccount) {
      // Test-Accounts erhalten automatisch das 3-Geräte-Abo
      _currentPlan = SubscriptionPlan.testAccount();
      _subscriptionStreamController.add(_currentPlan!);
      _saveSubscriptionToPrefs(_currentPlan!);

      // Auch in Firestore speichern, falls online
      try {
        await _subscriptionsCollection
            .doc(customerId)
            .set(_currentPlan!.toMap());
      } catch (e) {
        debugPrint(
            'SubscriptionService: Fehler beim Speichern des Test-Abos in Firestore: $e');
      }

      _isInitialized = true;
      return;
    }

    // Abonnement aus Firestore oder SharedPreferences laden
    await _loadCurrentSubscription();

    // InAppPurchase initialisieren
    final isAvailable = await _inAppPurchase.isAvailable();
    if (!isAvailable) {
      debugPrint('SubscriptionService: In-App-Käufe sind nicht verfügbar.');
      // Falls Käufe nicht verfügbar sind, trotzdem als initialisiert markieren
      _isInitialized = true;
      return;
    }

    // Produkte aus dem Play Store laden
    await _loadProducts();

    // Kaufstream abonnieren
    _purchaseStreamSubscription = _inAppPurchase.purchaseStream
        .listen(_handlePurchaseUpdates, onDone: () {
      _purchaseStreamSubscription?.cancel();
    }, onError: (error) {
      debugPrint('SubscriptionService: Fehler im Kaufstream: $error');
    });

    // Bestehende Käufe wiederherstellen
    await _restorePurchases();

    _isInitialized = true;
  }

  /// Lädt die verfügbaren Produkte aus dem Play Store
  Future<void> _loadProducts() async {
    try {
      // Produktkennungen für alle Abo-Typen (außer kostenlos)
      final Set<String> productIds = {
        SubscriptionType.oneDev.productId,
        SubscriptionType.threeDev.productId,
        SubscriptionType.fiveDev.productId,
      };

      final ProductDetailsResponse response =
          await _inAppPurchase.queryProductDetails(productIds);

      if (response.error != null) {
        debugPrint(
            'SubscriptionService: Fehler beim Laden der Produkte: ${response.error}');
        return;
      }

      if (response.productDetails.isEmpty) {
        debugPrint('SubscriptionService: Keine Produkte gefunden.');
        return;
      }

      _products = response.productDetails;
      debugPrint('SubscriptionService: ${_products.length} Produkte geladen.');
    } catch (e) {
      debugPrint('SubscriptionService: Fehler beim Laden der Produkte: $e');
    }
  }

  /// Behandelt Updates von Käufen
  Future<void> _handlePurchaseUpdates(
      List<PurchaseDetails> purchaseDetailsList) async {
    for (final PurchaseDetails purchaseDetails in purchaseDetailsList) {
      if (purchaseDetails.status == PurchaseStatus.pending) {
        // Kauf ausstehend, dem Benutzer Feedback geben
        debugPrint('SubscriptionService: Kauf ausstehend...');
      } else {
        if (purchaseDetails.status == PurchaseStatus.error) {
          // Fehler beim Kauf
          debugPrint(
              'SubscriptionService: Fehler beim Kauf: ${purchaseDetails.error}');
        } else if (purchaseDetails.status == PurchaseStatus.purchased ||
            purchaseDetails.status == PurchaseStatus.restored) {
          // Kauf erfolgreich oder wiederhergestellt
          await _verifyAndSavePurchase(purchaseDetails);
        }

        // Bestätigen, dass der Kauf verarbeitet wurde
        if (purchaseDetails.pendingCompletePurchase) {
          await _inAppPurchase.completePurchase(purchaseDetails);
        }
      }
    }
  }

  /// Prüft und speichert einen erfolgreichen Kauf
  Future<void> _verifyAndSavePurchase(PurchaseDetails purchase) async {
    try {
      // Im realen System würde hier eine Server-seitige Validierung erfolgen
      // Für unsere Demo-App vertrauen wir dem lokalen Status

      // Ermitteln, welches Abo gekauft wurde
      SubscriptionType? subscriptionType;

      if (purchase.productID == SubscriptionType.oneDev.productId) {
        subscriptionType = SubscriptionType.oneDev;
      } else if (purchase.productID == SubscriptionType.threeDev.productId) {
        subscriptionType = SubscriptionType.threeDev;
      } else if (purchase.productID == SubscriptionType.fiveDev.productId) {
        subscriptionType = SubscriptionType.fiveDev;
      }

      if (subscriptionType == null) {
        debugPrint(
            'SubscriptionService: Unbekanntes Produkt: ${purchase.productID}');
        return;
      }

      // Ablaufdatum festlegen (1 Jahr ab heute)
      final DateTime expiryDate = DateTime.now().add(Duration(days: 365));

      // Neuen Abonnement-Plan erstellen
      final SubscriptionPlan newPlan = SubscriptionPlan(
        type: subscriptionType,
        expiryDate: expiryDate,
        isActive: true,
      );

      // In Firestore speichern
      if (_customerId != null) {
        await _subscriptionsCollection.doc(_customerId).set(newPlan.toMap());
      }

      // Lokal speichern
      _currentPlan = newPlan;
      await _saveSubscriptionToPrefs(newPlan);

      // Stream-Listener benachrichtigen
      _subscriptionStreamController.add(newPlan);

      debugPrint(
          'SubscriptionService: Abo erfolgreich aktiviert: ${subscriptionType.displayName}');
    } catch (e) {
      debugPrint('SubscriptionService: Fehler bei der Kaufvalidierung: $e');
    }
  }

  /// Stellt bestehende Käufe wieder her
  Future<void> _restorePurchases() async {
    try {
      await _inAppPurchase.restorePurchases();
    } catch (e) {
      debugPrint(
          'SubscriptionService: Fehler beim Wiederherstellen der Käufe: $e');
    }
  }

  /// Lädt das aktuelle Abonnement aus Firestore oder SharedPreferences
  Future<void> _loadCurrentSubscription() async {
    if (_customerId == null) {
      debugPrint('SubscriptionService: Keine Kunden-ID verfügbar.');
      _currentPlan = SubscriptionPlan.free();
      return;
    }

    try {
      // Versuchen, aus Firestore zu laden
      final docSnapshot = await _subscriptionsCollection.doc(_customerId).get();

      if (docSnapshot.exists) {
        final data = docSnapshot.data() as Map<String, dynamic>;
        _currentPlan = SubscriptionPlan.fromMap(data);

        // Auch lokal speichern
        await _saveSubscriptionToPrefs(_currentPlan!);
      } else {
        // Aus SharedPreferences laden
        final prefs = await SharedPreferences.getInstance();
        final String? planJson = prefs.getString('subscription_plan');

        if (planJson != null) {
          _currentPlan = SubscriptionPlan.fromMap(json.decode(planJson));
        } else {
          // Kein Plan gefunden, kostenfreien Plan verwenden
          _currentPlan = SubscriptionPlan.free();
          await _saveSubscriptionToPrefs(_currentPlan!);
        }
      }

      // Prüfen, ob das Abo abgelaufen ist
      if (_currentPlan != null && !_currentPlan!.isValid) {
        // Wenn abgelaufen, auf den kostenlosen Plan zurücksetzen
        _currentPlan = SubscriptionPlan.free();
        await _saveSubscriptionToPrefs(_currentPlan!);
      }

      // Stream-Listener benachrichtigen
      _subscriptionStreamController.add(_currentPlan!);
    } catch (e) {
      debugPrint('SubscriptionService: Fehler beim Laden des Abonnements: $e');

      // Fallback: Kostenloser Plan
      _currentPlan = SubscriptionPlan.free();
      _subscriptionStreamController.add(_currentPlan!);
    }
  }

  /// Speichert den Abonnement-Plan in SharedPreferences
  Future<void> _saveSubscriptionToPrefs(SubscriptionPlan plan) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('subscription_plan', json.encode(plan.toMap()));
    } catch (e) {
      debugPrint(
          'SubscriptionService: Fehler beim Speichern des Abonnements: $e');
    }
  }

  /// Prüft, ob es sich um einen Test-Account handelt
  /// Prüft, ob es sich um einen Test-Account handelt
  Future<bool> _checkIfTestAccount(String customerId) async {
    debugPrint('SubscriptionService: Prüfe Test-Account für $customerId...');
    try {
      // 1. Zuerst prüfen, ob die Firebase-UID in der Firestore-Sammlung vorhanden ist
      // Falls wir die UID als Dokument-ID verwenden
      final testerDocById = await FirebaseFirestore.instance
          .collection('tester_accounts')
          .doc(customerId)
          .get();

      if (testerDocById.exists) {
        debugPrint(
            'SubscriptionService: $customerId ist als Tester in Firestore eingetragen (UID)');
        return true;
      }

      // 2. Falls nicht, versuchen wir die E-Mail des Benutzers zu holen und damit zu prüfen
      final firebaseUser = fb_auth.FirebaseAuth.instance.currentUser;
      if (firebaseUser != null && firebaseUser.email != null) {
        final userEmail = firebaseUser.email!;
        debugPrint('SubscriptionService: Prüfe auch die E-Mail $userEmail...');

        // Versuche zuerst die E-Mail als Dokument-ID
        final testerDocByEmail = await FirebaseFirestore.instance
            .collection('tester_accounts')
            .doc(userEmail)
            .get();

        if (testerDocByEmail.exists) {
          debugPrint(
              'SubscriptionService: $userEmail ist als Tester in Firestore eingetragen (E-Mail als Doc-ID)');
          return true;
        }

        // NEU: Prüfe alle Dokumente in der tester_accounts Collection auf E-Mail-Felder
        final querySnapshot = await FirebaseFirestore.instance
            .collection('tester_accounts')
            .get();

        for (var doc in querySnapshot.docs) {
          final data = doc.data();
          // Prüfe ob die E-Mail in einem der Felder des Dokuments vorkommt
          for (var value in data.values) {
            if (value == userEmail) {
              debugPrint(
                  'SubscriptionService: $userEmail ist als Tester in Firestore eingetragen (E-Mail als Feld)');
              return true;
            }
          }
        }
      }

      // Fallback für den Development-Modus
      if (kDebugMode) {
        // Für Debug-Zwecke bestimmte Test-IDs prüfen
        final List<String> debugTestAccounts = [
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>'
        ];
        final bool isDebugTestAccount = debugTestAccounts.contains(customerId);

        if (isDebugTestAccount) {
          debugPrint(
              'SubscriptionService: $customerId ist ein Debug-Test-Account');
          return true;
        }
      }

      debugPrint('SubscriptionService: $customerId ist kein Test-Account');
      return false;
    } catch (e) {
      debugPrint(
          'SubscriptionService: Fehler beim Prüfen des Test-Accounts: $e');
      return false;
    }
  }

  /// Gibt den aktuellen Abonnement-Plan zurück
  SubscriptionPlan getCurrentPlan() {
    return _currentPlan ?? SubscriptionPlan.free();
  }

  /// Prüft, ob die tägliche Verkaufsgrenze erreicht ist
  Future<bool> isWithinDailySalesLimit(int todaysSalesCount) async {
    final plan = getCurrentPlan();

    // Unbegrenzte Verkäufe
    if (plan.maxSalesPerDay < 0) return true;

    // Begrenzte Verkäufe
    return todaysSalesCount < plan.maxSalesPerDay;
  }

  /// Prüft, ob ein neues Gerät aktiviert werden kann
  Future<bool> canActivateDevice(int currentlyActiveDevices) async {
    final plan = getCurrentPlan();
    return currentlyActiveDevices < plan.maxDevices;
  }

  /// Startet den Kauf eines Abonnements
  Future<bool> purchaseSubscription(SubscriptionType type) async {
    try {
      // Produkt finden
      final String productId = type.productId;
      ProductDetails? product;
      try {
        product = _products.firstWhere(
          (prod) => prod.id == productId,
        );
      } catch (e) {
        product = null;
      }

      if (product == null) {
        debugPrint('SubscriptionService: Produkt nicht gefunden: $productId');
        return false;
      }

      // Kauf starten
      final PurchaseParam purchaseParam =
          PurchaseParam(productDetails: product);

      await _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam);
      return true;
    } catch (e) {
      debugPrint('SubscriptionService: Fehler beim Kauf: $e');
      return false;
    }
  }

  /// Gibt alle Abonnement-Optionen zurück
  List<SubscriptionType> getAllSubscriptionTypes() {
    return SubscriptionType.values;
  }

  /// Setzt den aktuellen Plan manuell (nur für Debug-Zwecke)
  Future<void> setDebugSubscriptionPlan(SubscriptionType type) async {
    if (!kDebugMode) return;

    final plan = SubscriptionPlan(
      type: type,
      expiryDate: type == SubscriptionType.free
          ? null
          : DateTime.now().add(Duration(days: 365)),
      isActive: true,
    );

    _currentPlan = plan;
    await _saveSubscriptionToPrefs(plan);
    _subscriptionStreamController.add(plan);
  }

  /// Gibt die Ressourcen des Services frei
  void dispose() {
    _purchaseStreamSubscription?.cancel();
    _subscriptionStreamController.close();
  }
}
