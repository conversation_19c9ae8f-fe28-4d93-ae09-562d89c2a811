{"logs": [{"outputFile": "com.example.troedelpos.app-mergeDebugResources-55:/values-en-rCA/values-en-rCA.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\62aeb653dda202444616439a5b81d504\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,113", "endOffsets": "162,276"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2762,2874", "endColumns": "111,113", "endOffsets": "2869,2983"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\596dc2cdab8b9d2ef83e7d6e0e134b8a\\transformed\\appcompat-1.6.1\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,46", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,4472", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,4550"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fe930d2507b0279f955567b6d5748a32\\transformed\\core-1.13.1\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,657,773", "endColumns": "95,101,98,98,103,101,115,100", "endOffsets": "146,248,347,446,550,652,768,869"}, "to": {"startLines": "31,32,33,34,35,36,37,47", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2988,3084,3186,3285,3384,3488,3590,4555", "endColumns": "95,101,98,98,103,101,115,100", "endOffsets": "3079,3181,3280,3379,3483,3585,3701,4651"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a75011f7f88673231c312008696729f9\\transformed\\preference-1.2.1\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,173,260,334,468,637,717", "endColumns": "67,86,73,133,168,79,75", "endOffsets": "168,255,329,463,632,712,788"}, "to": {"startLines": "38,40,44,45,48,49,50", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3706,3872,4264,4338,4656,4825,4905", "endColumns": "67,86,73,133,168,79,75", "endOffsets": "3769,3954,4333,4467,4820,4900,4976"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3f6219dea7a69dbc6d2bf4921723a7d5\\transformed\\browser-1.8.0\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,153,250,359", "endColumns": "97,96,108,98", "endOffsets": "148,245,354,453"}, "to": {"startLines": "39,41,42,43", "startColumns": "4,4,4,4", "startOffsets": "3774,3959,4056,4165", "endColumns": "97,96,108,98", "endOffsets": "3867,4051,4160,4259"}}]}]}