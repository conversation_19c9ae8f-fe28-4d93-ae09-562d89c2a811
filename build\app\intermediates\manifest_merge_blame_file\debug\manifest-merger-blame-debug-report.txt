1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.troedelpos"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="34" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\main\AndroidManifest.xml:5:5-67
15-->C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\main\AndroidManifest.xml:5:22-64
16    <!-- Ka<PERSON>a-Berechtigung für den Scanner -->
17    <uses-permission android:name="android.permission.CAMERA" /> <!-- Speicher-Berechtigungen für Tesseract-Modelle -->
17-->C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\main\AndroidManifest.xml:3:5-65
17-->C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\main\AndroidManifest.xml:3:22-62
18    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
18-->C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\main\AndroidManifest.xml:7:5-81
18-->C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\main\AndroidManifest.xml:7:22-78
19    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" /> <!-- Google Play Billing für In-App-Käufe und Abonnements -->
19-->C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\main\AndroidManifest.xml:8:5-80
19-->C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\main\AndroidManifest.xml:8:22-77
20    <uses-permission android:name="com.android.vending.BILLING" />
20-->C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\main\AndroidManifest.xml:10:5-67
20-->C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\main\AndroidManifest.xml:10:22-64
21    <!--
22 Required to query activities that can process text, see:
23         https://developer.android.com/training/package-visibility and
24         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
25         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
26    -->
27    <queries>
27-->C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\main\AndroidManifest.xml:71:5-76:15
28        <intent>
28-->C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\main\AndroidManifest.xml:72:9-75:18
29            <action android:name="android.intent.action.PROCESS_TEXT" />
29-->C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\main\AndroidManifest.xml:73:13-72
29-->C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\main\AndroidManifest.xml:73:21-70
30
31            <data android:mimeType="text/plain" />
31-->C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\main\AndroidManifest.xml:74:13-50
31-->C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\main\AndroidManifest.xml:74:19-48
32        </intent>
33        <intent>
33-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f79562eedfa06deadff6479d4f4acac\transformed\jetified-billing-7.1.1\AndroidManifest.xml:13:9-15:18
34            <action android:name="com.android.vending.billing.InAppBillingService.BIND" />
34-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f79562eedfa06deadff6479d4f4acac\transformed\jetified-billing-7.1.1\AndroidManifest.xml:14:13-91
34-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f79562eedfa06deadff6479d4f4acac\transformed\jetified-billing-7.1.1\AndroidManifest.xml:14:21-88
35        </intent>
36        <intent>
36-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f79562eedfa06deadff6479d4f4acac\transformed\jetified-billing-7.1.1\AndroidManifest.xml:16:9-18:18
37            <action android:name="com.google.android.apps.play.billingtestcompanion.BillingOverrideService.BIND" />
37-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f79562eedfa06deadff6479d4f4acac\transformed\jetified-billing-7.1.1\AndroidManifest.xml:17:13-116
37-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f79562eedfa06deadff6479d4f4acac\transformed\jetified-billing-7.1.1\AndroidManifest.xml:17:21-113
38        </intent>
39    </queries>
40
41    <uses-permission android:name="android.permission.RECORD_AUDIO" />
41-->[:camera_android] C:\Users\<USER>\Documents\environments\troedelpos\build\camera_android\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-71
41-->[:camera_android] C:\Users\<USER>\Documents\environments\troedelpos\build\camera_android\intermediates\merged_manifest\debug\AndroidManifest.xml:8:22-68
42    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
42-->[:connectivity_plus] C:\Users\<USER>\Documents\environments\troedelpos\build\connectivity_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-79
42-->[:connectivity_plus] C:\Users\<USER>\Documents\environments\troedelpos\build\connectivity_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:7:22-76
43    <uses-permission android:name="android.permission.WAKE_LOCK" />
43-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:25:5-68
43-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:25:22-65
44    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
44-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:27:5-81
44-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:27:22-78
45    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
45-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
45-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:22-74
46    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
46-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a2a94c3d197e9b946fa4f683bc141b6b\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:5-98
46-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a2a94c3d197e9b946fa4f683bc141b6b\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:22-95
47
48    <permission
48-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\fe930d2507b0279f955567b6d5748a32\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
49        android:name="com.example.troedelpos.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
49-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\fe930d2507b0279f955567b6d5748a32\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
50        android:protectionLevel="signature" />
50-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\fe930d2507b0279f955567b6d5748a32\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
51
52    <uses-permission android:name="com.example.troedelpos.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
52-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\fe930d2507b0279f955567b6d5748a32\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
52-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\fe930d2507b0279f955567b6d5748a32\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
53
54    <application
55        android:name="android.app.Application"
56        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
56-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\fe930d2507b0279f955567b6d5748a32\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
57        android:debuggable="true"
58        android:extractNativeLibs="false"
59        android:icon="@mipmap/ic_launcher"
60        android:label="troedelpos"
61        android:requestLegacyExternalStorage="true" >
62        <activity
63            android:name="com.example.troedelpos.MainActivity"
64            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
65            android:exported="true"
66            android:hardwareAccelerated="true"
67            android:launchMode="singleTop"
68            android:taskAffinity=""
69            android:theme="@style/LaunchTheme"
70            android:windowSoftInputMode="adjustResize" >
71
72            <!--
73                 Specifies an Android theme to apply to this Activity as soon as
74                 the Android process has started. This theme is visible to the user
75                 while the Flutter UI initializes. After that, this theme continues
76                 to determine the Window background behind the Flutter UI.
77            -->
78            <meta-data
79                android:name="io.flutter.embedding.android.NormalTheme"
80                android:resource="@style/NormalTheme" />
81
82            <intent-filter>
83                <action android:name="android.intent.action.MAIN" />
84
85                <category android:name="android.intent.category.LAUNCHER" />
86            </intent-filter>
87        </activity>
88
89        <!-- Google Sign-In Konfiguration -->
90        <meta-data
91            android:name="com.google.android.gms.version"
92            android:value="@integer/google_play_services_version" />
93
94        <!-- Für Google Sign-In -->
95        <activity
96            android:name="io.flutter.plugins.googlesignin.GoogleSignInActivity"
97            android:exported="true"
98            android:theme="@style/Theme.AppCompat.Light.NoActionBar" >
99            <intent-filter>
100                <action android:name="android.intent.action.VIEW" />
100-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:36:17-69
100-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:36:25-66
101
102                <category android:name="android.intent.category.DEFAULT" />
102-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:38:17-76
102-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:38:27-73
103                <category android:name="android.intent.category.BROWSABLE" />
103-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:39:17-78
103-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:39:27-75
104                <!-- Anpassen des Schemas mit der korrekten Client-ID -->
105                <data
105-->C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\main\AndroidManifest.xml:74:13-50
106                    android:host="oauth2redirect"
107                    android:scheme="com.googleusercontent.apps.237608338542-njomlna9ujmq68jc3bla4fm10a34tkut" />
108            </intent-filter>
109        </activity>
110
111        <!--
112             Don't delete the meta-data below.
113             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
114        -->
115        <meta-data
116            android:name="flutterEmbedding"
117            android:value="2" />
118
119        <service
119-->[:cloud_firestore] C:\Users\<USER>\Documents\environments\troedelpos\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:19
120            android:name="com.google.firebase.components.ComponentDiscoveryService"
120-->[:cloud_firestore] C:\Users\<USER>\Documents\environments\troedelpos\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:8:18-89
121            android:directBootAware="true"
121-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3bb114678107da1ef99f97798dc193bb\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
122            android:exported="false" >
122-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:68:13-37
123            <meta-data
123-->[:cloud_firestore] C:\Users\<USER>\Documents\environments\troedelpos\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
124                android:name="com.google.firebase.components:io.flutter.plugins.firebase.firestore.FlutterFirebaseFirestoreRegistrar"
124-->[:cloud_firestore] C:\Users\<USER>\Documents\environments\troedelpos\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-134
125                android:value="com.google.firebase.components.ComponentRegistrar" />
125-->[:cloud_firestore] C:\Users\<USER>\Documents\environments\troedelpos\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
126            <meta-data
126-->[:firebase_auth] C:\Users\<USER>\Documents\environments\troedelpos\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
127                android:name="com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar"
127-->[:firebase_auth] C:\Users\<USER>\Documents\environments\troedelpos\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-124
128                android:value="com.google.firebase.components.ComponentRegistrar" />
128-->[:firebase_auth] C:\Users\<USER>\Documents\environments\troedelpos\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
129            <meta-data
129-->[:firebase_core] C:\Users\<USER>\Documents\environments\troedelpos\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
130                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
130-->[:firebase_core] C:\Users\<USER>\Documents\environments\troedelpos\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-124
131                android:value="com.google.firebase.components.ComponentRegistrar" />
131-->[:firebase_core] C:\Users\<USER>\Documents\environments\troedelpos\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
132            <meta-data
132-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:69:13-71:85
133                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
133-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:70:17-109
134                android:value="com.google.firebase.components.ComponentRegistrar" />
134-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:71:17-82
135            <meta-data
135-->[com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\a2bc3fa9b88a89bfa05736f8fd4a3121\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:17:13-19:85
136                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
136-->[com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\a2bc3fa9b88a89bfa05736f8fd4a3121\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:18:17-122
137                android:value="com.google.firebase.components.ComponentRegistrar" />
137-->[com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\a2bc3fa9b88a89bfa05736f8fd4a3121\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:19:17-82
138            <meta-data
138-->[com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\a2bc3fa9b88a89bfa05736f8fd4a3121\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:20:13-22:85
139                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
139-->[com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\a2bc3fa9b88a89bfa05736f8fd4a3121\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:21:17-111
140                android:value="com.google.firebase.components.ComponentRegistrar" />
140-->[com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\a2bc3fa9b88a89bfa05736f8fd4a3121\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:22:17-82
141            <meta-data
141-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\550b14c5117666deb34b8cf6b1829c33\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
142                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
142-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\550b14c5117666deb34b8cf6b1829c33\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
143                android:value="com.google.firebase.components.ComponentRegistrar" />
143-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\550b14c5117666deb34b8cf6b1829c33\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
144            <meta-data
144-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3bb114678107da1ef99f97798dc193bb\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
145                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
145-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3bb114678107da1ef99f97798dc193bb\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
146                android:value="com.google.firebase.components.ComponentRegistrar" />
146-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3bb114678107da1ef99f97798dc193bb\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
147        </service>
148        <!--
149           Declares a provider which allows us to store files to share in
150           '.../caches/share_plus' and grant the receiving action access
151        -->
152        <provider
152-->[:share_plus] C:\Users\<USER>\Documents\environments\troedelpos\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:13:9-21:20
153            android:name="dev.fluttercommunity.plus.share.ShareFileProvider"
153-->[:share_plus] C:\Users\<USER>\Documents\environments\troedelpos\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-77
154            android:authorities="com.example.troedelpos.flutter.share_provider"
154-->[:share_plus] C:\Users\<USER>\Documents\environments\troedelpos\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:15:13-74
155            android:exported="false"
155-->[:share_plus] C:\Users\<USER>\Documents\environments\troedelpos\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:16:13-37
156            android:grantUriPermissions="true" >
156-->[:share_plus] C:\Users\<USER>\Documents\environments\troedelpos\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:17:13-47
157            <meta-data
157-->[:share_plus] C:\Users\<USER>\Documents\environments\troedelpos\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:18:13-20:68
158                android:name="android.support.FILE_PROVIDER_PATHS"
158-->[:share_plus] C:\Users\<USER>\Documents\environments\troedelpos\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:19:17-67
159                android:resource="@xml/flutter_share_file_paths" />
159-->[:share_plus] C:\Users\<USER>\Documents\environments\troedelpos\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:20:17-65
160        </provider>
161        <!--
162           This manifest declared broadcast receiver allows us to use an explicit
163           Intent when creating a PendingItent to be informed of the user's choice
164        -->
165        <receiver
165-->[:share_plus] C:\Users\<USER>\Documents\environments\troedelpos\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:26:9-32:20
166            android:name="dev.fluttercommunity.plus.share.SharePlusPendingIntent"
166-->[:share_plus] C:\Users\<USER>\Documents\environments\troedelpos\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:27:13-82
167            android:exported="false" >
167-->[:share_plus] C:\Users\<USER>\Documents\environments\troedelpos\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:28:13-37
168            <intent-filter>
168-->[:share_plus] C:\Users\<USER>\Documents\environments\troedelpos\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:29:13-31:29
169                <action android:name="EXTRA_CHOSEN_COMPONENT" />
169-->[:share_plus] C:\Users\<USER>\Documents\environments\troedelpos\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:30:17-65
169-->[:share_plus] C:\Users\<USER>\Documents\environments\troedelpos\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:30:25-62
170            </intent-filter>
171        </receiver>
172
173        <activity
173-->[:url_launcher_android] C:\Users\<USER>\Documents\environments\troedelpos\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-11:74
174            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
174-->[:url_launcher_android] C:\Users\<USER>\Documents\environments\troedelpos\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-74
175            android:exported="false"
175-->[:url_launcher_android] C:\Users\<USER>\Documents\environments\troedelpos\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-37
176            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
176-->[:url_launcher_android] C:\Users\<USER>\Documents\environments\troedelpos\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-71
177        <activity
177-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:29:9-46:20
178            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
178-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:30:13-80
179            android:excludeFromRecents="true"
179-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:31:13-46
180            android:exported="true"
180-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:32:13-36
181            android:launchMode="singleTask"
181-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:33:13-44
182            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
182-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:34:13-72
183            <intent-filter>
183-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:35:13-45:29
184                <action android:name="android.intent.action.VIEW" />
184-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:36:17-69
184-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:36:25-66
185
186                <category android:name="android.intent.category.DEFAULT" />
186-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:38:17-76
186-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:38:27-73
187                <category android:name="android.intent.category.BROWSABLE" />
187-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:39:17-78
187-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:39:27-75
188
189                <data
189-->C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\main\AndroidManifest.xml:74:13-50
190                    android:host="firebase.auth"
191                    android:path="/"
192                    android:scheme="genericidp" />
193            </intent-filter>
194        </activity>
195        <activity
195-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:47:9-64:20
196            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
196-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:48:13-79
197            android:excludeFromRecents="true"
197-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:49:13-46
198            android:exported="true"
198-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:50:13-36
199            android:launchMode="singleTask"
199-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:51:13-44
200            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
200-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:52:13-72
201            <intent-filter>
201-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:53:13-63:29
202                <action android:name="android.intent.action.VIEW" />
202-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:36:17-69
202-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:36:25-66
203
204                <category android:name="android.intent.category.DEFAULT" />
204-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:38:17-76
204-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:38:27-73
205                <category android:name="android.intent.category.BROWSABLE" />
205-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:39:17-78
205-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2965fed515c2f11ae4a18582574a8989\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:39:27-75
206
207                <data
207-->C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\main\AndroidManifest.xml:74:13-50
208                    android:host="firebase.auth"
209                    android:path="/"
210                    android:scheme="recaptcha" />
211            </intent-filter>
212        </activity>
213
214        <uses-library
214-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\69a6d671be6bdd1a1fdb40a1fed71722\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
215            android:name="androidx.window.extensions"
215-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\69a6d671be6bdd1a1fdb40a1fed71722\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
216            android:required="false" />
216-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\69a6d671be6bdd1a1fdb40a1fed71722\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
217        <uses-library
217-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\69a6d671be6bdd1a1fdb40a1fed71722\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
218            android:name="androidx.window.sidecar"
218-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\69a6d671be6bdd1a1fdb40a1fed71722\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
219            android:required="false" />
219-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\69a6d671be6bdd1a1fdb40a1fed71722\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
220
221        <service
221-->[com.google.mlkit:digital-ink-recognition:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8fc52eb327e2202155f3dbc243d8187a\transformed\jetified-digital-ink-recognition-18.1.0\AndroidManifest.xml:12:9-18:19
222            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
222-->[com.google.mlkit:digital-ink-recognition:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8fc52eb327e2202155f3dbc243d8187a\transformed\jetified-digital-ink-recognition-18.1.0\AndroidManifest.xml:13:13-91
223            android:directBootAware="true"
223-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\076acdf74b13b0d0be55167d9cf30c82\transformed\jetified-common-18.11.0\AndroidManifest.xml:17:13-43
224            android:exported="false" >
224-->[com.google.mlkit:digital-ink-recognition:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8fc52eb327e2202155f3dbc243d8187a\transformed\jetified-digital-ink-recognition-18.1.0\AndroidManifest.xml:14:13-37
225            <meta-data
225-->[com.google.mlkit:digital-ink-recognition:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8fc52eb327e2202155f3dbc243d8187a\transformed\jetified-digital-ink-recognition-18.1.0\AndroidManifest.xml:15:13-17:85
226                android:name="com.google.firebase.components:com.google.mlkit.vision.digitalink.internal.DigitalInkRecognitionRegistrar"
226-->[com.google.mlkit:digital-ink-recognition:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8fc52eb327e2202155f3dbc243d8187a\transformed\jetified-digital-ink-recognition-18.1.0\AndroidManifest.xml:16:17-137
227                android:value="com.google.firebase.components.ComponentRegistrar" />
227-->[com.google.mlkit:digital-ink-recognition:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8fc52eb327e2202155f3dbc243d8187a\transformed\jetified-digital-ink-recognition-18.1.0\AndroidManifest.xml:17:17-82
228            <meta-data
228-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed51e9ea00ee12a4c2d9fc12ca35b3c2\transformed\jetified-play-services-mlkit-text-recognition-common-19.1.0\AndroidManifest.xml:12:13-14:85
229                android:name="com.google.firebase.components:com.google.mlkit.vision.text.internal.TextRegistrar"
229-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed51e9ea00ee12a4c2d9fc12ca35b3c2\transformed\jetified-play-services-mlkit-text-recognition-common-19.1.0\AndroidManifest.xml:13:17-114
230                android:value="com.google.firebase.components.ComponentRegistrar" />
230-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed51e9ea00ee12a4c2d9fc12ca35b3c2\transformed\jetified-play-services-mlkit-text-recognition-common-19.1.0\AndroidManifest.xml:14:17-82
231            <meta-data
231-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\e62b8171866d636f8ed6a0bfd220b8f2\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
232                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
232-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\e62b8171866d636f8ed6a0bfd220b8f2\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:13:17-124
233                android:value="com.google.firebase.components.ComponentRegistrar" />
233-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\e62b8171866d636f8ed6a0bfd220b8f2\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:14:17-82
234            <meta-data
234-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\076acdf74b13b0d0be55167d9cf30c82\transformed\jetified-common-18.11.0\AndroidManifest.xml:20:13-22:85
235                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
235-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\076acdf74b13b0d0be55167d9cf30c82\transformed\jetified-common-18.11.0\AndroidManifest.xml:21:17-120
236                android:value="com.google.firebase.components.ComponentRegistrar" />
236-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\076acdf74b13b0d0be55167d9cf30c82\transformed\jetified-common-18.11.0\AndroidManifest.xml:22:17-82
237        </service>
238
239        <provider
239-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:31:9-39:20
240            android:name="androidx.startup.InitializationProvider"
240-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:32:13-67
241            android:authorities="com.example.troedelpos.androidx-startup"
241-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:33:13-68
242            android:exported="false" >
242-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:34:13-37
243            <meta-data
243-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
244                android:name="androidx.work.WorkManagerInitializer"
244-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
245                android:value="androidx.startup" />
245-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
246            <meta-data
246-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\cb4f9b7546ddd8ec180df336ad808fa4\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
247                android:name="androidx.emoji2.text.EmojiCompatInitializer"
247-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\cb4f9b7546ddd8ec180df336ad808fa4\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
248                android:value="androidx.startup" />
248-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\cb4f9b7546ddd8ec180df336ad808fa4\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
249            <meta-data
249-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\952d74a4f84de990b1d8333e6126b61e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
250                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
250-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\952d74a4f84de990b1d8333e6126b61e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
251                android:value="androidx.startup" />
251-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\952d74a4f84de990b1d8333e6126b61e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
252            <meta-data
252-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
253                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
253-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
254                android:value="androidx.startup" />
254-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
255        </provider>
256
257        <service
257-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
258            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
258-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
259            android:directBootAware="false"
259-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
260            android:enabled="@bool/enable_system_alarm_service_default"
260-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
261            android:exported="false" />
261-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
262        <service
262-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
263            android:name="androidx.work.impl.background.systemjob.SystemJobService"
263-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
264            android:directBootAware="false"
264-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
265            android:enabled="@bool/enable_system_job_service_default"
265-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
266            android:exported="true"
266-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
267            android:permission="android.permission.BIND_JOB_SERVICE" />
267-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
268        <service
268-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
269            android:name="androidx.work.impl.foreground.SystemForegroundService"
269-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
270            android:directBootAware="false"
270-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
271            android:enabled="@bool/enable_system_foreground_service_default"
271-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
272            android:exported="false" />
272-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
273
274        <receiver
274-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
275            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
275-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
276            android:directBootAware="false"
276-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
277            android:enabled="true"
277-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
278            android:exported="false" />
278-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
279        <receiver
279-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
280            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
280-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
281            android:directBootAware="false"
281-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
282            android:enabled="false"
282-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
283            android:exported="false" >
283-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
284            <intent-filter>
284-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
285                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
285-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
285-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
286                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
286-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
286-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
287            </intent-filter>
288        </receiver>
289        <receiver
289-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
290            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
290-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
291            android:directBootAware="false"
291-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
292            android:enabled="false"
292-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
293            android:exported="false" >
293-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
294            <intent-filter>
294-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
295                <action android:name="android.intent.action.BATTERY_OKAY" />
295-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
295-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
296                <action android:name="android.intent.action.BATTERY_LOW" />
296-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
296-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
297            </intent-filter>
298        </receiver>
299        <receiver
299-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
300            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
300-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
301            android:directBootAware="false"
301-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
302            android:enabled="false"
302-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
303            android:exported="false" >
303-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
304            <intent-filter>
304-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
305                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
305-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
305-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
306                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
306-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
306-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
307            </intent-filter>
308        </receiver>
309        <receiver
309-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
310            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
310-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
311            android:directBootAware="false"
311-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
312            android:enabled="false"
312-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
313            android:exported="false" >
313-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
314            <intent-filter>
314-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
315                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
315-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
315-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
316            </intent-filter>
317        </receiver>
318        <receiver
318-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
319            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
319-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
320            android:directBootAware="false"
320-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
321            android:enabled="false"
321-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
322            android:exported="false" >
322-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
323            <intent-filter>
323-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
324                <action android:name="android.intent.action.BOOT_COMPLETED" />
324-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:17-79
324-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:25-76
325                <action android:name="android.intent.action.TIME_SET" />
325-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
325-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
326                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
326-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
326-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
327            </intent-filter>
328        </receiver>
329        <receiver
329-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
330            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
330-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
331            android:directBootAware="false"
331-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
332            android:enabled="@bool/enable_system_alarm_service_default"
332-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
333            android:exported="false" >
333-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
334            <intent-filter>
334-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
335                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
335-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
335-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
336            </intent-filter>
337        </receiver>
338        <receiver
338-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
339            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
339-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
340            android:directBootAware="false"
340-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
341            android:enabled="true"
341-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
342            android:exported="true"
342-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
343            android:permission="android.permission.DUMP" >
343-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
344            <intent-filter>
344-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
345                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
345-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
345-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
346            </intent-filter>
347        </receiver>
348
349        <service
349-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\811d866da11c4adc1e237c7be48ea17a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
350            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
350-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\811d866da11c4adc1e237c7be48ea17a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
351            android:enabled="true"
351-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\811d866da11c4adc1e237c7be48ea17a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
352            android:exported="false" >
352-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\811d866da11c4adc1e237c7be48ea17a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
353            <meta-data
353-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\811d866da11c4adc1e237c7be48ea17a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
354                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
354-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\811d866da11c4adc1e237c7be48ea17a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
355                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
355-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\811d866da11c4adc1e237c7be48ea17a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
356        </service>
357
358        <activity
358-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\811d866da11c4adc1e237c7be48ea17a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
359            android:name="androidx.credentials.playservices.HiddenActivity"
359-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\811d866da11c4adc1e237c7be48ea17a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
360            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
360-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\811d866da11c4adc1e237c7be48ea17a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
361            android:enabled="true"
361-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\811d866da11c4adc1e237c7be48ea17a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
362            android:exported="false"
362-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\811d866da11c4adc1e237c7be48ea17a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
363            android:fitsSystemWindows="true"
363-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\811d866da11c4adc1e237c7be48ea17a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
364            android:theme="@style/Theme.Hidden" >
364-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\811d866da11c4adc1e237c7be48ea17a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
365        </activity>
366        <activity
366-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3998f03731776e641b0412496162b18\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
367            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
367-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3998f03731776e641b0412496162b18\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
368            android:excludeFromRecents="true"
368-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3998f03731776e641b0412496162b18\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
369            android:exported="false"
369-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3998f03731776e641b0412496162b18\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
370            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
370-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3998f03731776e641b0412496162b18\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
371        <!--
372            Service handling Google Sign-In user revocation. For apps that do not integrate with
373            Google Sign-In, this service will never be started.
374        -->
375        <service
375-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3998f03731776e641b0412496162b18\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
376            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
376-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3998f03731776e641b0412496162b18\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
377            android:exported="true"
377-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3998f03731776e641b0412496162b18\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
378            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
378-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3998f03731776e641b0412496162b18\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
379            android:visibleToInstantApps="true" />
379-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3998f03731776e641b0412496162b18\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
380
381        <meta-data
381-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f79562eedfa06deadff6479d4f4acac\transformed\jetified-billing-7.1.1\AndroidManifest.xml:22:9-24:37
382            android:name="com.google.android.play.billingclient.version"
382-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f79562eedfa06deadff6479d4f4acac\transformed\jetified-billing-7.1.1\AndroidManifest.xml:23:13-73
383            android:value="7.1.1" />
383-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f79562eedfa06deadff6479d4f4acac\transformed\jetified-billing-7.1.1\AndroidManifest.xml:24:13-34
384
385        <activity
385-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f79562eedfa06deadff6479d4f4acac\transformed\jetified-billing-7.1.1\AndroidManifest.xml:26:9-30:75
386            android:name="com.android.billingclient.api.ProxyBillingActivity"
386-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f79562eedfa06deadff6479d4f4acac\transformed\jetified-billing-7.1.1\AndroidManifest.xml:27:13-78
387            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
387-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f79562eedfa06deadff6479d4f4acac\transformed\jetified-billing-7.1.1\AndroidManifest.xml:28:13-96
388            android:exported="false"
388-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f79562eedfa06deadff6479d4f4acac\transformed\jetified-billing-7.1.1\AndroidManifest.xml:29:13-37
389            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
389-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f79562eedfa06deadff6479d4f4acac\transformed\jetified-billing-7.1.1\AndroidManifest.xml:30:13-72
390        <activity
390-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f79562eedfa06deadff6479d4f4acac\transformed\jetified-billing-7.1.1\AndroidManifest.xml:31:9-35:75
391            android:name="com.android.billingclient.api.ProxyBillingActivityV2"
391-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f79562eedfa06deadff6479d4f4acac\transformed\jetified-billing-7.1.1\AndroidManifest.xml:32:13-80
392            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
392-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f79562eedfa06deadff6479d4f4acac\transformed\jetified-billing-7.1.1\AndroidManifest.xml:33:13-96
393            android:exported="false"
393-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f79562eedfa06deadff6479d4f4acac\transformed\jetified-billing-7.1.1\AndroidManifest.xml:34:13-37
394            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
394-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f79562eedfa06deadff6479d4f4acac\transformed\jetified-billing-7.1.1\AndroidManifest.xml:35:13-72
395
396        <provider
396-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\076acdf74b13b0d0be55167d9cf30c82\transformed\jetified-common-18.11.0\AndroidManifest.xml:9:9-13:38
397            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
397-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\076acdf74b13b0d0be55167d9cf30c82\transformed\jetified-common-18.11.0\AndroidManifest.xml:10:13-78
398            android:authorities="com.example.troedelpos.mlkitinitprovider"
398-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\076acdf74b13b0d0be55167d9cf30c82\transformed\jetified-common-18.11.0\AndroidManifest.xml:11:13-69
399            android:exported="false"
399-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\076acdf74b13b0d0be55167d9cf30c82\transformed\jetified-common-18.11.0\AndroidManifest.xml:12:13-37
400            android:initOrder="99" />
400-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\076acdf74b13b0d0be55167d9cf30c82\transformed\jetified-common-18.11.0\AndroidManifest.xml:13:13-35
401
402        <activity
402-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b368049dbdfe7c192fa848707b37b6\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
403            android:name="com.google.android.gms.common.api.GoogleApiActivity"
403-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b368049dbdfe7c192fa848707b37b6\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
404            android:exported="false"
404-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b368049dbdfe7c192fa848707b37b6\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
405            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
405-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29b368049dbdfe7c192fa848707b37b6\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
406
407        <provider
407-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3bb114678107da1ef99f97798dc193bb\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
408            android:name="com.google.firebase.provider.FirebaseInitProvider"
408-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3bb114678107da1ef99f97798dc193bb\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
409            android:authorities="com.example.troedelpos.firebaseinitprovider"
409-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3bb114678107da1ef99f97798dc193bb\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
410            android:directBootAware="true"
410-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3bb114678107da1ef99f97798dc193bb\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
411            android:exported="false"
411-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3bb114678107da1ef99f97798dc193bb\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
412            android:initOrder="100" />
412-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3bb114678107da1ef99f97798dc193bb\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
413
414        <receiver
414-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
415            android:name="androidx.profileinstaller.ProfileInstallReceiver"
415-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
416            android:directBootAware="false"
416-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
417            android:enabled="true"
417-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
418            android:exported="true"
418-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
419            android:permission="android.permission.DUMP" >
419-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
420            <intent-filter>
420-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
421                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
421-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
421-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
422            </intent-filter>
423            <intent-filter>
423-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
424                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
424-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
424-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
425            </intent-filter>
426            <intent-filter>
426-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
427                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
427-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
427-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
428            </intent-filter>
429            <intent-filter>
429-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
430                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
430-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
430-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
431            </intent-filter>
432        </receiver>
433
434        <service
434-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\5dc29b04cf00cdbc8783e786639e2a69\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
435            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
435-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\5dc29b04cf00cdbc8783e786639e2a69\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
436            android:exported="false" >
436-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\5dc29b04cf00cdbc8783e786639e2a69\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
437            <meta-data
437-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\5dc29b04cf00cdbc8783e786639e2a69\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
438                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
438-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\5dc29b04cf00cdbc8783e786639e2a69\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
439                android:value="cct" />
439-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\5dc29b04cf00cdbc8783e786639e2a69\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
440        </service>
441        <service
441-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\ee969d6aefaa82cfa2f0e33fc4ab16ac\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
442            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
442-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\ee969d6aefaa82cfa2f0e33fc4ab16ac\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
443            android:exported="false"
443-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\ee969d6aefaa82cfa2f0e33fc4ab16ac\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
444            android:permission="android.permission.BIND_JOB_SERVICE" >
444-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\ee969d6aefaa82cfa2f0e33fc4ab16ac\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
445        </service>
446
447        <receiver
447-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\ee969d6aefaa82cfa2f0e33fc4ab16ac\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
448            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
448-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\ee969d6aefaa82cfa2f0e33fc4ab16ac\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
449            android:exported="false" />
449-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\ee969d6aefaa82cfa2f0e33fc4ab16ac\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
450
451        <service
451-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\e3cf089486f7ad4e20f60677fe72bdf6\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
452            android:name="androidx.room.MultiInstanceInvalidationService"
452-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\e3cf089486f7ad4e20f60677fe72bdf6\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
453            android:directBootAware="true"
453-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\e3cf089486f7ad4e20f60677fe72bdf6\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
454            android:exported="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
454-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\e3cf089486f7ad4e20f60677fe72bdf6\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
455        <activity
455-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\17add4f136784dea2dd439689278c96a\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:14:9-18:65
456            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
456-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\17add4f136784dea2dd439689278c96a\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:15:13-93
457            android:exported="false"
457-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\17add4f136784dea2dd439689278c96a\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:16:13-37
458            android:stateNotNeeded="true"
458-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\17add4f136784dea2dd439689278c96a\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:17:13-42
459            android:theme="@style/Theme.PlayCore.Transparent" />
459-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\17add4f136784dea2dd439689278c96a\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:18:13-62
460    </application>
461
462</manifest>
