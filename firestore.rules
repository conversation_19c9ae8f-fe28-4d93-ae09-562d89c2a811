rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Hilfsfunktion: Prüft, ob der Benutzer angemeldet ist
    function isSignedIn() {
      return request.auth != null;
    }
    
    // Hilfsfunktion: Prüft, ob der aktuelle Benutzer Eigentümer des Kundenmandanten ist
    function isCustomerOwner() {
      // Prüft, ob der Benutzer im users-Dokument als Eigentümer markiert ist
      let userId = request.auth.uid;
      let requestedCustomerId = request.resource.data.customer_id;
      return exists(/databases/$(database)/documents/users/$(userId)) &&
             get(/databases/$(database)/documents/users/$(userId)).data.customerId == requestedCustomerId &&
             get(/databases/$(database)/documents/users/$(userId)).data.additionalData.isOwner == true;
    }
    
    // Hilfsfunktion: Prüft, ob der Benutzer zum selben Mandanten gehört wie das Dokument
    function belongsToSameCustomer(customerId) {
      let userId = request.auth.uid;
      return exists(/databases/$(database)/documents/users/$(userId)) &&
             get(/databases/$(database)/documents/users/$(userId)).data.customerId == customerId;
    }
    
    // Hilfsfunktion: Prüft, ob customerID in einem neuen Dokument korrekt ist
    function isValidCustomerIdForCreate() {
      let userId = request.auth.uid;
      let requestedCustomerId = request.resource.data.customer_id;
      return exists(/databases/$(database)/documents/users/$(userId)) &&
             get(/databases/$(database)/documents/users/$(userId)).data.customerId == requestedCustomerId;
    }
    
    // Hilfsfunktion: Prüft, ob customerID in einem zu aktualisierenden Dokument unverändert ist
    function isValidCustomerIdForUpdate() {
      // Prüfe zuerst, ob das Dokument bereits existiert
      return resource == null || request.resource.data.customer_id == resource.data.customer_id;
    }
    
    // Hilfsfunktion: Prüft, ob alle Bedingungen für ein Update erfüllt sind
    function canUpdate(customerId) {
      return isSignedIn() && 
             belongsToSameCustomer(customerId) && 
             isValidCustomerIdForUpdate();
    }
    
    // Users-Collection
    match /users/{userId} {
      // Nur eigenes Profil lesen oder aktualisieren
      allow read: if isSignedIn() && request.auth.uid == userId;
      allow create: if isSignedIn() && request.auth.uid == userId;
      // Vereinfachte Update-Regel: Benutzer dürfen ihr eigenes Dokument aktualisieren
      allow update: if isSignedIn() && request.auth.uid == userId;
      
      // Nur Eigentümer können andere Benutzer im selben Mandanten lesen
      allow read: if isSignedIn() && 
                    exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
                    get(/databases/$(database)/documents/users/$(request.auth.uid)).data.additionalData.isOwner == true &&
                    get(/databases/$(database)/documents/users/$(userId)).data.customerId == 
                    get(/databases/$(database)/documents/users/$(request.auth.uid)).data.customerId;
    }
    
    // Tester Accounts Collection - NEU
    match /tester_accounts/{email} {
      // Jeder angemeldete Benutzer darf lesen, aber nur Admin darf schreiben
      allow read: if isSignedIn();
      // Schreibzugriff nur über Firebase-Konsole oder Admin-API
    }
    
    // Subscriptions Collection - NEU
    match /subscriptions/{userId} {
      // Benutzer dürfen nur ihr eigenes Abonnement lesen und schreiben
      allow read, write: if isSignedIn() && request.auth.uid == userId;
    }
    
    // Devices-Collection
    match /devices/{deviceId} {
      // Lesen: Erlaubt für Benutzer des selben Mandanten
      allow read: if isSignedIn() && 
                    belongsToSameCustomer(resource.data.customer_id);
      
      // Erstellen: Erlaubt für angemeldete Benutzer
      allow create: if isSignedIn() && isValidCustomerIdForCreate();
      
      // Aktualisieren: Erlaubt für Benutzer des selben Mandanten
      allow update: if canUpdate(resource.data.customer_id);
      
      // Löschen: Erlaubt für Benutzer des selben Mandanten
      allow delete: if isSignedIn() && belongsToSameCustomer(resource.data.customer_id);
    }
    
    // Flea Markets Collection
    match /flea_markets/{marketId} {
      allow read: if isSignedIn() && 
                    belongsToSameCustomer(resource.data.customer_id);
      allow create: if isSignedIn() && isValidCustomerIdForCreate();
      allow update: if canUpdate(resource.data.customer_id);
      allow delete: if isSignedIn() && belongsToSameCustomer(resource.data.customer_id);
    }
    
    // Cashiers Collection
    match /cashiers/{cashierId} {
      allow read: if isSignedIn() && 
                    belongsToSameCustomer(resource.data.customer_id);
      allow create: if isSignedIn() && isValidCustomerIdForCreate();
      allow update: if canUpdate(resource.data.customer_id);
      allow delete: if isSignedIn() && belongsToSameCustomer(resource.data.customer_id);
    }
    
    // Vendors Collection
    match /vendors/{vendorId} {
      allow read: if isSignedIn() && 
                    belongsToSameCustomer(resource.data.customer_id);
      allow create: if isSignedIn() && isValidCustomerIdForCreate();
      allow update: if canUpdate(resource.data.customer_id);
      allow delete: if isSignedIn() && belongsToSameCustomer(resource.data.customer_id);
    }
    
    // Sales Collection
    match /sales/{saleId} {
      allow read: if isSignedIn() && 
                    belongsToSameCustomer(resource.data.customer_id);
      allow create: if isSignedIn() && isValidCustomerIdForCreate();
      allow update: if canUpdate(resource.data.customer_id);
      allow delete: if isSignedIn() && belongsToSameCustomer(resource.data.customer_id);
    }
    
    // Sale Items Collection
    match /sale_items/{itemId} {
      allow read: if isSignedIn() && 
                    belongsToSameCustomer(resource.data.customer_id);
      allow create: if isSignedIn() && isValidCustomerIdForCreate();
      allow update: if canUpdate(resource.data.customer_id);
      allow delete: if isSignedIn() && belongsToSameCustomer(resource.data.customer_id);
    }
    
    // Product Categories Collection
    match /product_categories/{categoryId} {
      allow read: if isSignedIn() && 
                    belongsToSameCustomer(resource.data.customer_id);
      allow create: if isSignedIn() && isValidCustomerIdForCreate();
      allow update: if canUpdate(resource.data.customer_id);
      allow delete: if isSignedIn() && belongsToSameCustomer(resource.data.customer_id);
    }
  }
}
