// Keine Imports nötig

/// Modell für die verschiedenen Abonnement-Pläne der App
enum SubscriptionType {
  free,       // Kostenloser Testmodus
  oneDev,     // 1 Gerät Abo
  threeDev,   // 3 Geräte Abo
  fiveDev,    // 5 Geräte Abo
}

/// Erweitert die SubscriptionType Enum mit Informationen zu jedem Abo-Typ
extension SubscriptionTypeExtension on SubscriptionType {
  /// Gibt die maximal erlaubte Anzahl an aktiven Geräten für diesen Abo-Typ zurück
  int get maxDevices {
    switch (this) {
      case SubscriptionType.free:
        return 1;
      case SubscriptionType.oneDev:
        return 1;
      case SubscriptionType.threeDev:
        return 3;
      case SubscriptionType.fiveDev:
        return 5;
    }
  }

  /// Gibt die maximal erlaubte Anzahl an Verkäufen pro Tag für diesen Abo-Typ zurück
  int get maxSalesPerDay {
    switch (this) {
      case SubscriptionType.free:
        return 10;
      case SubscriptionType.oneDev:
      case SubscriptionType.threeDev:
      case SubscriptionType.fiveDev:
        return -1; // Unbegrenzt (-1 bedeutet keine Limitierung)
    }
  }

  /// Gibt den Namen des Abo-Typs zurück
  String get displayName {
    switch (this) {
      case SubscriptionType.free:
        return "Kostenloser Testmodus";
      case SubscriptionType.oneDev:
        return "1 Gerät Abo";
      case SubscriptionType.threeDev:
        return "3 Geräte Abo";
      case SubscriptionType.fiveDev:
        return "5 Geräte Abo";
    }
  }

  /// Gibt die Produktkennung im Play Store für diesen Abo-Typ zurück
  String get productId {
    switch (this) {
      case SubscriptionType.free:
        return ""; // Kein Produkt für den kostenlosen Modus
      case SubscriptionType.oneDev:
        return "de.troedelpos.abo.one_device";
      case SubscriptionType.threeDev:
        return "de.troedelpos.abo.three_devices";
      case SubscriptionType.fiveDev:
        return "de.troedelpos.abo.five_devices";
    }
  }

  /// Gibt den Preis des Abos als String zurück (für Anzeigezwecke)
  String get priceDisplay {
    switch (this) {
      case SubscriptionType.free:
        return "Kostenlos";
      case SubscriptionType.oneDev:
        return "29,99 € / Jahr";
      case SubscriptionType.threeDev:
        return "69,99 € / Jahr";
      case SubscriptionType.fiveDev:
        return "99,99 € / Jahr";
    }
  }

  /// Gibt die Beschreibung des Abo-Typs zurück
  String get description {
    switch (this) {
      case SubscriptionType.free:
        return "1 aktives Gerät und maximal 10 Verkäufe am Tag";
      case SubscriptionType.oneDev:
        return "Ein Gerät aktiv, ein Jahr im Abo, unbegrenzte Verkäufe";
      case SubscriptionType.threeDev:
        return "Drei Geräte aktiv, ein Jahr im Abo, unbegrenzte Verkäufe";
      case SubscriptionType.fiveDev:
        return "Fünf Geräte aktiv, ein Jahr im Abo, unbegrenzte Verkäufe";
    }
  }
}

/// Modell für einen Abonnement-Plan mit zusätzlichen Metadaten
class SubscriptionPlan {
  final SubscriptionType type;
  final DateTime? expiryDate;
  final bool isActive;
  
  SubscriptionPlan({
    required this.type,
    this.expiryDate,
    this.isActive = true,
  });
  
  /// Erzeugt einen kostenlosen Testmodus-Plan
  factory SubscriptionPlan.free() {
    return SubscriptionPlan(
      type: SubscriptionType.free,
      isActive: true,
    );
  }

  /// Erzeugt einen Test-Account Plan mit 3 Geräten
  factory SubscriptionPlan.testAccount() {
    return SubscriptionPlan(
      type: SubscriptionType.threeDev,
      expiryDate: DateTime.now().add(Duration(days: 365)), // Ein Jahr gültig
      isActive: true,
    );
  }
  
  /// Erstellt eine Kopie dieses Plans mit aktualisierten Werten
  SubscriptionPlan copyWith({
    SubscriptionType? type,
    DateTime? expiryDate,
    bool? isActive,
  }) {
    return SubscriptionPlan(
      type: type ?? this.type,
      expiryDate: expiryDate ?? this.expiryDate,
      isActive: isActive ?? this.isActive,
    );
  }
  
  /// Konvertiert die Klasse in eine Map für die Speicherung
  Map<String, dynamic> toMap() {
    return {
      'type': type.index,
      'expiry_date': expiryDate?.millisecondsSinceEpoch,
      'is_active': isActive,
    };
  }
  
  /// Erstellt einen Plan aus einer Map
  factory SubscriptionPlan.fromMap(Map<String, dynamic> map) {
    return SubscriptionPlan(
      type: SubscriptionType.values[map['type'] ?? 0],
      expiryDate: map['expiry_date'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(map['expiry_date'])
          : null,
      isActive: map['is_active'] ?? false,
    );
  }
  
  /// Erstellt einen Plan aus einem Firestore-Dokument
  factory SubscriptionPlan.fromFirestore(dynamic doc) {
    final data = doc.data() as Map<String, dynamic>;
    return SubscriptionPlan.fromMap(data);
  }
  
  /// Prüft, ob das Abonnement noch gültig ist
  bool get isValid {
    if (!isActive) return false;
    if (expiryDate == null) return type == SubscriptionType.free;
    return DateTime.now().isBefore(expiryDate!);
  }
  
  /// Gibt die maximale Anzahl an Geräten zurück, die aktiviert werden können
  int get maxDevices => type.maxDevices;
  
  /// Gibt die maximale Anzahl an Verkäufen pro Tag zurück
  int get maxSalesPerDay => type.maxSalesPerDay;
  
  @override
  String toString() {
    return 'SubscriptionPlan{type: ${type.displayName}, expiryDate: $expiryDate, isActive: $isActive}';
  }
}
