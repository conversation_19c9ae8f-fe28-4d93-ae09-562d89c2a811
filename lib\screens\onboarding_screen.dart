import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:dots_indicator/dots_indicator.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:url_launcher/url_launcher.dart';
import '../services/auth_service.dart';
import '../services/onboarding_service.dart';
import '../services/session_service.dart';
import '../services/device_service.dart';
import '../utils/onboarding_style_helper.dart';
import '../widgets/showcase_helper.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({Key? key}) : super(key: key);

  @override
  _OnboardingScreenState createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _pageController = PageController();
  final OnboardingService _onboardingService = OnboardingService();
  final DeviceService _deviceService = DeviceService();
  final SessionService _sessionService = SessionService();
  int _currentPage = 0;
  bool _isRequestingPermissions = false;
  bool _isLoggingIn = false;
  int _maxActiveDevices = 3; // Standardwert, wird später dynamisch geladen

  @override
  void initState() {
    super.initState();
    _loadMaxActiveDevices();
    _loadCurrentStep();
  }

  Future<void> _loadMaxActiveDevices() async {
    // Hier könnte später die maximale Anzahl aktiver Geräte aus einem Abonnement geladen werden
    // Für jetzt verwenden wir den Standardwert 3
    setState(() {
      _maxActiveDevices = 3;
    });
  }

  Future<void> _loadCurrentStep() async {
    final currentStep = await _onboardingService.getCurrentStep();
    setState(() {
      _currentPage = currentStep;
    });
    // PageController zur richtigen Seite navigieren
    if (_pageController.hasClients) {
      _pageController.jumpToPage(currentStep);
    } else {
      // Falls der PageController noch nicht bereit ist, warten wir kurz
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_pageController.hasClients) {
          _pageController.jumpToPage(currentStep);
        }
      });
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  Future<void> _requestPermissions() async {
    setState(() {
      _isRequestingPermissions = true;
    });

    // Kamerazugriff anfordern
    await Permission.camera.request();

    // Speicherzugriff anfordern
    await Permission.storage.request();

    setState(() {
      _isRequestingPermissions = false;
    });

    // Wenn alle Berechtigungen erteilt wurden oder der Benutzer sie abgelehnt hat,
    // fahren wir trotzdem fort (wir zeigen später entsprechende Hinweise)
    _goToNextPage();
  }

  Future<void> _signInWithGoogle() async {
    setState(() {
      _isLoggingIn = true;
    });

    try {
      // Google-Anmeldung durchführen
      final success = await _sessionService.signIn();

      if (!mounted) return; // Sicherheitscheck nach await

      if (success) {
        // Zur nächsten Seite wechseln
        _goToNextPage();
      } else {
        // Anmeldung fehlgeschlagen
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
                'Anmeldung fehlgeschlagen. Bitte versuchen Sie es erneut.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Fehler bei der Anmeldung: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoggingIn = false;
        });
      }
    }
  }

  void _goToNextPage() {
    if (_currentPage < 4) {
      final nextPage = _currentPage + 1;
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      // Aktuellen Schritt speichern
      _onboardingService.setCurrentStep(nextPage);
    } else {
      _completeOnboarding();
    }
  }

  Future<void> _completeOnboarding() async {
    // Phase 1 des Onboardings als abgeschlossen markieren
    await _onboardingService.setPhase1Completed(true);

    if (!mounted) return; // Sicherheitscheck nach await

    // Zum DeviceNamingScreen navigieren
    Navigator.of(context).pushReplacementNamed('/name_device');
  }

  // URL öffnen Methode
  Future<void> _launchURL(String url) async {
    final Uri uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      debugPrint('Could not launch $url');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          Expanded(
            child: PageView(
              controller: _pageController,
              onPageChanged: (int page) {
                setState(() {
                  _currentPage = page;
                });
                // Aktuellen Schritt speichern
                _onboardingService.setCurrentStep(page);
              },
              children: [
                _buildWelcomePage(),
                _buildGoogleLoginPage(),
                _buildPermissionsPage(),
                _buildDeviceSetupPage(),
                _buildSubscriptionPage(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWelcomePage() {
    final theme = Theme.of(context);

    return Stack(
      children: [
        // Hintergrundverlauf mit einheitlichem Styling
        Container(
          decoration: BoxDecoration(
            gradient: OnboardingStyleHelper.getBackgroundGradient(context),
          ),
        ),

        // Hauptinhalt mit einheitlichem Card-Styling
        Center(
          child: AspectRatio(
            aspectRatio: 1.0, // 1:1 Seitenverhältnis
            child: Container(
              margin: const EdgeInsets.fromLTRB(
                  20, 40, 20, 40), // Mehr Abstand oben/unten
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(20),
                    blurRadius: 10,
                    spreadRadius: 1,
                  ),
                ],
              ),
              child: Column(
                children: [
                  // Leere Leiste oben für mehr Platz
                  const SizedBox(height: 16),

                  // Hauptinhalt
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24),
                      child: Column(
                        children: [
                          // Platz für Illustration
                          Expanded(
                            flex: 3,
                            child: Center(
                              child: Image.asset(
                                'assets/images/onboarding_illustration.png',
                                fit: BoxFit.contain,
                                errorBuilder: (context, error, stackTrace) {
                                  // Fallback, wenn das Bild nicht gefunden wird - quadratisch
                                  return AspectRatio(
                                    aspectRatio:
                                        1.0, // 1:1 Seitenverhältnis für das Bild
                                    child: Container(
                                      decoration: BoxDecoration(
                                        color: theme.colorScheme.primary
                                            .withAlpha(20),
                                        borderRadius: BorderRadius.circular(20),
                                      ),
                                      child: Stack(
                                        children: [
                                          // Hintergrund-Elemente
                                          Positioned(
                                            top: 20,
                                            left: 20,
                                            child: Container(
                                              width: 60,
                                              height: 60,
                                              decoration: BoxDecoration(
                                                color: theme.colorScheme.primary
                                                    .withAlpha(40),
                                                shape: BoxShape.circle,
                                              ),
                                            ),
                                          ),
                                          Positioned(
                                            bottom: 30,
                                            right: 30,
                                            child: Container(
                                              width: 80,
                                              height: 80,
                                              decoration: BoxDecoration(
                                                color: theme.colorScheme.primary
                                                    .withAlpha(30),
                                                shape: BoxShape.circle,
                                              ),
                                            ),
                                          ),
                                          // Hauptinhalt
                                          Center(
                                            child: Column(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                Container(
                                                  padding:
                                                      const EdgeInsets.all(16),
                                                  decoration: BoxDecoration(
                                                    color: theme
                                                        .colorScheme.primary
                                                        .withAlpha(40),
                                                    shape: BoxShape.circle,
                                                  ),
                                                  child: Icon(
                                                    Icons.shopping_cart,
                                                    size: 60,
                                                    color: theme
                                                        .colorScheme.primary,
                                                  ),
                                                ),
                                                const SizedBox(height: 24),
                                                Text(
                                                  'Trödix Kasse',
                                                  style: TextStyle(
                                                    color: theme
                                                        .colorScheme.primary,
                                                    fontWeight: FontWeight.bold,
                                                    fontSize: 22,
                                                  ),
                                                ),
                                                const SizedBox(height: 8),
                                                Text(
                                                  'Einfach. Effizient. Zuverlässig.',
                                                  style: TextStyle(
                                                    color: theme
                                                        .colorScheme.onSurface
                                                        .withAlpha(150),
                                                    fontSize: 14,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),

                          // Willkommenstext
                          Expanded(
                            flex: 1,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  'Willkommen bei Trödix Kasse!',
                                  style:
                                      theme.textTheme.headlineSmall?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 24,
                                    color: Colors.black87,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  'Verwalten Sie Ihre Trödelmarktverkäufe einfach und effizient.',
                                  style: theme.textTheme.bodyLarge?.copyWith(
                                    color: Colors.black54,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          ),

                          // Get Started Button
                          Padding(
                            padding: const EdgeInsets.only(bottom: 32),
                            child: ElevatedButton(
                              onPressed: () => _goToNextPage(),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: const Color(
                                    0xFF2A2A3A), // Dunkelblau/Schwarz
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 40, vertical: 16),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(30),
                                ),
                                elevation: 0,
                              ),
                              child: const Text(
                                'Starten',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildGoogleLoginPage() {
    final theme = Theme.of(context);

    return Stack(
      children: [
        // Hintergrundverlauf mit Primary Color (wie beim ersten Screen)
        Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Theme.of(context).colorScheme.primary, // Türkis (Primary Color)
                const Color(0xFFB2DFDB), // Helleres Türkis
              ],
            ),
          ),
        ),

        // Hauptinhalt mit weißem Hintergrund - quadratische Card (1:1)
        Center(
          child: AspectRatio(
            aspectRatio: 1.0, // 1:1 Seitenverhältnis
            child: Container(
              margin: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(30),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(20),
                    blurRadius: 10,
                    spreadRadius: 1,
                  ),
                ],
              ),
              child: Column(
                children: [
                  // Leere Leiste oben für mehr Platz
                  const SizedBox(height: 16),

                  // Hauptinhalt
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24),
                      child: Column(
                        children: [
                          // Icon Bereich - einheitlich mit anderen Screens
                          Container(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            child: Container(
                              padding: const EdgeInsets.all(20),
                              decoration: BoxDecoration(
                                color: theme.colorScheme.primary.withAlpha(40),
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                Icons.account_circle,
                                size: 60,
                                color: theme.colorScheme.primary,
                              ),
                            ),
                          ),

                          // Titel Bereich - einheitlich mit anderen Screens
                          Padding(
                            padding: const EdgeInsets.only(bottom: 16),
                            child: Text(
                              'Google Anmeldung',
                              style: theme.textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                                fontSize: 20,
                                color: Colors.black87,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),

                          // Text Bereich - flexibel
                          Expanded(
                            child: SingleChildScrollView(
                              child: Column(
                                children: [
                                  Text(
                                    'Melden Sie sich mit Ihrem Google-Konto an, um Ihre Daten zu sichern und auf mehreren Geräten nutzen zu können.',
                                    style: theme.textTheme.bodyLarge?.copyWith(
                                      color: Colors.black54,
                                      fontSize: 16,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ),
                            ),
                          ),

                          // Mit Google anmelden Button
                          Padding(
                            padding: const EdgeInsets.only(bottom: 36),
                            child: ElevatedButton(
                              onPressed:
                                  _isLoggingIn ? null : _signInWithGoogle,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: const Color(
                                    0xFF2A2A3A), // Gleiche Farbe wie "Starten" Button
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 32, vertical: 16),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(30),
                                ),
                                elevation: 0,
                              ),
                              child: _isLoggingIn
                                  ? const SizedBox(
                                      width: 20,
                                      height: 20,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor:
                                            AlwaysStoppedAnimation<Color>(
                                                Colors.white),
                                      ),
                                    )
                                  : Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Image.asset(
                                          'assets/images/google_logo.png',
                                          height: 20,
                                          width: 20,
                                          errorBuilder:
                                              (context, error, stackTrace) {
                                            // Fallback wenn Google Logo nicht gefunden wird
                                            return const Icon(
                                              Icons.account_circle,
                                              size: 20,
                                              color: Colors.white,
                                            );
                                          },
                                        ),
                                        const SizedBox(width: 12),
                                        const Text(
                                          'mit Google anmelden',
                                          style: TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ],
                                    ),
                            ),
                          ),

                          // Datenschutz-Text mit Links
                          Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 16),
                            child: RichText(
                              textAlign: TextAlign.center,
                              text: TextSpan(
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: Colors.black54,
                                  fontSize: 12,
                                ),
                                children: [
                                  const TextSpan(
                                    text:
                                        'Mit der Anmeldung bestätigen Sie unsere ',
                                  ),
                                  TextSpan(
                                    text: 'Nutzungsvereinbarung',
                                    style: TextStyle(
                                      color: theme.colorScheme.primary,
                                      decoration: TextDecoration.underline,
                                    ),
                                    recognizer: TapGestureRecognizer()
                                      ..onTap = () => _launchURL(
                                          'https://sites.google.com/view/famigoapps/vendea-fleamarket-pos/terms-of-service-vendea'),
                                  ),
                                  const TextSpan(
                                    text: ', sowie unsere ',
                                  ),
                                  TextSpan(
                                    text: 'Datenschutzbestimmungen',
                                    style: TextStyle(
                                      color: theme.colorScheme.primary,
                                      decoration: TextDecoration.underline,
                                    ),
                                    recognizer: TapGestureRecognizer()
                                      ..onTap = () => _launchURL(
                                          'https://sites.google.com/view/famigoapps/vendea-fleamarket-pos/privacy-policy-of-vendea'),
                                  ),
                                  const TextSpan(
                                    text: '.',
                                  ),
                                ],
                              ),
                            ),
                          ),

                          // Dots Indicator in der Card
                          Padding(
                            padding: const EdgeInsets.only(bottom: 16),
                            child: DotsIndicator(
                              dotsCount: 5,
                              position: _currentPage,
                              decorator: DotsDecorator(
                                activeColor: theme.colorScheme.primary,
                                color: theme.colorScheme.primary.withAlpha(100),
                                size: const Size.square(8.0),
                                activeSize: const Size(16.0, 8.0),
                                activeShape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(4.0),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPermissionsPage() {
    final theme = Theme.of(context);

    return Stack(
      children: [
        // Hintergrundverlauf mit einheitlichem Styling
        Container(
          decoration: BoxDecoration(
            gradient: OnboardingStyleHelper.getBackgroundGradient(context),
          ),
        ),

        // Hauptinhalt mit einheitlichem Card-Styling
        Center(
          child: AspectRatio(
            aspectRatio: 1.0, // 1:1 Seitenverhältnis
            child: Container(
              margin: const EdgeInsets.fromLTRB(
                  20, 40, 20, 40), // Gleiche Margins wie andere Screens
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(20),
                    blurRadius: 10,
                    spreadRadius: 1,
                  ),
                ],
              ),
              child: Column(
                children: [
                  // Leere Leiste oben für mehr Platz
                  const SizedBox(height: 16),

                  // Hauptinhalt
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24),
                      child: Column(
                        children: [
                          // Icon Bereich - kompakter
                          Container(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            child: Container(
                              padding: const EdgeInsets.all(20),
                              decoration: BoxDecoration(
                                color: theme.colorScheme.primary.withAlpha(40),
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                Icons.security,
                                size: 60,
                                color: theme.colorScheme.primary,
                              ),
                            ),
                          ),

                          // Titel Bereich - kompakter
                          Padding(
                            padding: const EdgeInsets.only(bottom: 16),
                            child: Text(
                              'Benötigte Berechtigungen',
                              style: theme.textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                                fontSize: 20,
                                color: Colors.black87,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),

                          // Berechtigungen Liste - flexibel
                          Expanded(
                            child: SingleChildScrollView(
                              child: Column(
                                children: [
                                  _buildPermissionItem(
                                    icon: Icons.camera_alt,
                                    title: 'Kamerazugriff',
                                    description:
                                        'Für das Scannen von Artikel-Etiketten',
                                    benefit:
                                        'Beschleunigt die Erfassung von Verkäufernummern und Preisen',
                                  ),
                                  const SizedBox(height: 12),
                                  _buildPermissionItem(
                                    icon: Icons.folder,
                                    title: 'Speicherzugriff',
                                    description:
                                        'Für den Export von Berichten als Excel-Dateien',
                                    benefit:
                                        'Ermöglicht das Speichern und Teilen Ihrer Auswertungen',
                                  ),
                                ],
                              ),
                            ),
                          ),

                          // Button Bereich - kompakt
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            child: _isRequestingPermissions
                                ? Column(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      CircularProgressIndicator(
                                        color: theme.colorScheme.primary,
                                      ),
                                      const SizedBox(height: 12),
                                      Text(
                                        'Berechtigungen werden angefragt...',
                                        style: theme.textTheme.bodyMedium
                                            ?.copyWith(
                                          color: Colors.black54,
                                        ),
                                      ),
                                    ],
                                  )
                                : ElevatedButton(
                                    onPressed: _requestPermissions,
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: const Color(0xFF2A2A3A),
                                      foregroundColor: Colors.white,
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 32, vertical: 16),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(30),
                                      ),
                                      elevation: 0,
                                    ),
                                    child: const Text(
                                      'Berechtigungen erteilen',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                          ),

                          // Dots Indicator in der Card
                          DotsIndicator(
                            dotsCount: 5,
                            position: _currentPage,
                            decorator: DotsDecorator(
                              activeColor: theme.colorScheme.primary,
                              color: theme.colorScheme.primary.withAlpha(100),
                              size: const Size.square(8.0),
                              activeSize: const Size(16.0, 8.0),
                              activeShape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(4.0),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDeviceSetupPage() {
    final theme = Theme.of(context);

    return Stack(
      children: [
        // Hintergrundverlauf mit einheitlichem Styling
        Container(
          decoration: BoxDecoration(
            gradient: OnboardingStyleHelper.getBackgroundGradient(context),
          ),
        ),

        // Hauptinhalt mit einheitlichem Card-Styling
        Center(
          child: AspectRatio(
            aspectRatio: 1.0, // 1:1 Seitenverhältnis
            child: Container(
              margin: const EdgeInsets.fromLTRB(
                  20, 40, 20, 40), // Gleiche Margins wie andere Screens
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(20),
                    blurRadius: 10,
                    spreadRadius: 1,
                  ),
                ],
              ),
              child: Column(
                children: [
                  // Leere Leiste oben für mehr Platz
                  const SizedBox(height: 16),

                  // Hauptinhalt
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24),
                      child: Column(
                        children: [
                          // Icon Bereich - einheitlich mit anderen Screens
                          Container(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            child: Container(
                              padding: const EdgeInsets.all(20),
                              decoration: BoxDecoration(
                                color: theme.colorScheme.primary.withAlpha(40),
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                Icons.devices,
                                size: 60,
                                color: theme.colorScheme.primary,
                              ),
                            ),
                          ),

                          // Titel Bereich - einheitlich mit anderen Screens
                          Padding(
                            padding: const EdgeInsets.only(bottom: 16),
                            child: Text(
                              'Geräteeinrichtung',
                              style: theme.textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                                fontSize: 20,
                                color: Colors.black87,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),

                          // Text Bereich - flexibel
                          Expanded(
                            child: SingleChildScrollView(
                              child: Column(
                                children: [
                                  Text(
                                    'Benennen Sie dieses Gerät, um es später von anderen Kassen unterscheiden zu können.',
                                    style: theme.textTheme.bodyLarge?.copyWith(
                                      color: Colors.black54,
                                      fontSize: 16,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                  const SizedBox(height: 12),
                                  Text(
                                    'Sie können bis zu $_maxActiveDevices Geräte gleichzeitig aktiv nutzen.',
                                    style: theme.textTheme.bodyMedium?.copyWith(
                                      color: Colors.black45,
                                      fontSize: 12,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ),
                            ),
                          ),

                          // Button Bereich - kompakt
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            child: ElevatedButton(
                              onPressed: _completeOnboarding,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: const Color(0xFF2A2A3A),
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 32, vertical: 16),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(30),
                                ),
                                elevation: 0,
                              ),
                              child: const Text(
                                'Gerät jetzt benennen',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),

                          // Dots Indicator in der Card
                          Padding(
                            padding: const EdgeInsets.only(bottom: 16),
                            child: DotsIndicator(
                              dotsCount: 5,
                              position: _currentPage,
                              decorator: DotsDecorator(
                                activeColor: theme.colorScheme.primary,
                                color: theme.colorScheme.primary.withAlpha(100),
                                size: const Size.square(8.0),
                                activeSize: const Size(16.0, 8.0),
                                activeShape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(4.0),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSubscriptionPage() {
    final theme = Theme.of(context);

    return Stack(
      children: [
        // Hintergrundverlauf mit einheitlichem Styling
        Container(
          decoration: BoxDecoration(
            gradient: OnboardingStyleHelper.getBackgroundGradient(context),
          ),
        ),

        // Hauptinhalt mit einheitlichem Card-Styling
        Center(
          child: AspectRatio(
            aspectRatio: 1.0, // 1:1 Seitenverhältnis
            child: Container(
              margin: const EdgeInsets.fromLTRB(20, 40, 20, 40),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(20),
                    blurRadius: 10,
                    spreadRadius: 1,
                  ),
                ],
              ),
              child: Column(
                children: [
                  // Leere Leiste oben für mehr Platz
                  const SizedBox(height: 16),

                  // Hauptinhalt
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24),
                      child: Column(
                        children: [
                          // Icon Bereich - einheitlich mit anderen Screens
                          Container(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            child: Container(
                              padding: const EdgeInsets.all(20),
                              decoration: BoxDecoration(
                                color: theme.colorScheme.primary.withAlpha(40),
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                Icons.star,
                                size: 60,
                                color: theme.colorScheme.primary,
                              ),
                            ),
                          ),

                          // Titel Bereich - einheitlich mit anderen Screens
                          Padding(
                            padding: const EdgeInsets.only(bottom: 16),
                            child: Text(
                              'Wählen Sie Ihr Abo',
                              style: theme.textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                                fontSize: 20,
                                color: Colors.black87,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),

                          // Abo-Optionen - flexibel
                          Expanded(
                            child: SingleChildScrollView(
                              child: Column(
                                children: [
                                  _buildSubscriptionOption(
                                    title: 'Kostenlos',
                                    description:
                                        'Grundfunktionen für kleine Märkte',
                                    features: [
                                      'Bis zu 50 Verkäufe pro Tag',
                                      'Basis-Berichte'
                                    ],
                                    price: 'Kostenlos',
                                    isRecommended: false,
                                    onTap: () => _selectSubscription('free'),
                                  ),
                                  const SizedBox(height: 12),
                                  _buildSubscriptionOption(
                                    title: 'Premium',
                                    description:
                                        'Vollständige Funktionen für professionelle Märkte',
                                    features: [
                                      'Unbegrenzte Verkäufe',
                                      'Erweiterte Berichte',
                                      'Cloud-Backup'
                                    ],
                                    price: '9,99€/Monat',
                                    isRecommended: true,
                                    onTap: () => _selectSubscription('premium'),
                                  ),
                                ],
                              ),
                            ),
                          ),

                          // Dots Indicator in der Card
                          DotsIndicator(
                            dotsCount: 5,
                            position: _currentPage,
                            decorator: DotsDecorator(
                              activeColor: theme.colorScheme.primary,
                              color: theme.colorScheme.primary.withAlpha(100),
                              size: const Size.square(8.0),
                              activeSize: const Size(16.0, 8.0),
                              activeShape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(4.0),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSubscriptionOption({
    required String title,
    required String description,
    required List<String> features,
    required String price,
    required bool isRecommended,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);

    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(
            color: isRecommended
                ? theme.colorScheme.primary
                : Colors.grey.shade300,
            width: isRecommended ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(12),
          color: isRecommended
              ? theme.colorScheme.primary.withAlpha(10)
              : Colors.white,
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    title,
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: isRecommended
                          ? theme.colorScheme.primary
                          : Colors.black87,
                    ),
                  ),
                  if (isRecommended)
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        'Empfohlen',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                description,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: Colors.black54,
                ),
              ),
              const SizedBox(height: 12),
              ...features.map((feature) => Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Row(
                      children: [
                        Icon(
                          Icons.check,
                          size: 16,
                          color: theme.colorScheme.primary,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          feature,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: Colors.black54,
                          ),
                        ),
                      ],
                    ),
                  )),
              const SizedBox(height: 12),
              Text(
                price,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.primary,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _selectSubscription(String subscriptionType) {
    // Hier würde die Abo-Auswahl gespeichert werden
    // Für jetzt gehen wir einfach zum nächsten Screen
    _completeOnboarding();
  }

  Widget _buildPermissionItem({
    required IconData icon,
    required String title,
    required String description,
    required String benefit,
  }) {
    final theme = Theme.of(context);
    final primaryColor = theme.colorScheme.primary;

    return Card(
      elevation: 0.2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: Colors.grey.shade300, width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                // Verwende withAlpha statt withOpacity
                color: primaryColor.withAlpha(51), // 0.2 * 255 = 51
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                color: primaryColor,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurface
                          .withAlpha(179), // 0.7 * 255 = 179
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    benefit,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: primaryColor,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
