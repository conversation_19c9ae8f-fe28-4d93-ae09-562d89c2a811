{"logs": [{"outputFile": "com.example.troedelpos.app-mergeDebugResources-55:/values-cs/values-cs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\29b368049dbdfe7c192fa848707b37b6\\transformed\\jetified-play-services-base-18.5.0\\res\\values-cs\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,451,572,678,827,950,1058,1155,1326,1433,1593,1717,1874,2025,2089,2152", "endColumns": "101,155,120,105,148,122,107,96,170,106,159,123,156,150,63,62,81", "endOffsets": "294,450,571,677,826,949,1057,1154,1325,1432,1592,1716,1873,2024,2088,2151,2233"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3761,3867,4027,4152,4262,4415,4542,4654,4898,5073,5184,5348,5476,5637,5792,5860,5927", "endColumns": "105,159,124,109,152,126,111,100,174,110,163,127,160,154,67,66,85", "endOffsets": "3862,4022,4147,4257,4410,4537,4649,4750,5068,5179,5343,5471,5632,5787,5855,5922,6008"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\62aeb653dda202444616439a5b81d504\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,121", "endOffsets": "159,281"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2799,2908", "endColumns": "108,121", "endOffsets": "2903,3025"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a75011f7f88673231c312008696729f9\\transformed\\preference-1.2.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,178,276,354,501,670,754", "endColumns": "72,97,77,146,168,83,80", "endOffsets": "173,271,349,496,665,749,830"}, "to": {"startLines": "56,58,62,63,66,67,68", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6013,6188,6604,6682,7013,7182,7266", "endColumns": "72,97,77,146,168,83,80", "endOffsets": "6081,6281,6677,6824,7177,7261,7342"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fe930d2507b0279f955567b6d5748a32\\transformed\\core-1.13.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,455,560,667,786", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "148,250,351,450,555,662,781,882"}, "to": {"startLines": "31,32,33,34,35,36,37,65", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3030,3128,3230,3331,3430,3535,3642,6912", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "3123,3225,3326,3425,3530,3637,3756,7008"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\19d4c2635369e2aa123936ad1ab3a080\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-cs\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4755", "endColumns": "142", "endOffsets": "4893"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\596dc2cdab8b9d2ef83e7d6e0e134b8a\\transformed\\appcompat-1.6.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,424,510,615,732,810,886,977,1070,1165,1259,1353,1446,1541,1638,1729,1820,1904,2008,2120,2219,2325,2436,2538,2701,2799", "endColumns": "106,101,109,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "207,309,419,505,610,727,805,881,972,1065,1160,1254,1348,1441,1536,1633,1724,1815,1899,2003,2115,2214,2320,2431,2533,2696,2794,2877"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,424,510,615,732,810,886,977,1070,1165,1259,1353,1446,1541,1638,1729,1820,1904,2008,2120,2219,2325,2436,2538,2701,6829", "endColumns": "106,101,109,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "207,309,419,505,610,727,805,881,972,1065,1160,1254,1348,1441,1536,1633,1724,1815,1899,2003,2115,2214,2320,2431,2533,2696,2794,6907"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3f6219dea7a69dbc6d2bf4921723a7d5\\transformed\\browser-1.8.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,157,260,374", "endColumns": "101,102,113,100", "endOffsets": "152,255,369,470"}, "to": {"startLines": "57,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "6086,6286,6389,6503", "endColumns": "101,102,113,100", "endOffsets": "6183,6384,6498,6599"}}]}]}