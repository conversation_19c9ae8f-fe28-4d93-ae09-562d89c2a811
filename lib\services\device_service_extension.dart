import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/subscription_plan.dart';
import 'device_service.dart';
import 'subscription_service.dart';
import 'user_service.dart';

/// Erweiterungen für den DeviceService zur Integration mit dem SubscriptionService
extension DeviceServiceExtension on DeviceService {
  // Static-Instanz des SubscriptionService für die Abo-Verwaltung
  static final SubscriptionService _subscriptionService = SubscriptionService();

  /// Initialisiert den SubscriptionService
  Future<void> initializeSubscription() async {
    // UserService verwenden, um die Kunden-ID zu erhalten
    final userService = UserService();
    String? customerId = await userService.getCurrentCustomerId();
    if (customerId?.isNotEmpty == true) {
      await _subscriptionService.initialize(customerId);
      
      // Nach der Initialisierung überprüfen, ob die Anzahl der aktiven Geräte
      // dem aktuellen Abonnement entspricht und ggf. anpassen
      await enforceDeviceLimit();
    }
  }
  
  /// Erzwingt das Einhalten des Gerätelimits des aktuellen Abonnements
  /// Deaktiviert überzählige Geräte, wenn zu viele aktiv sind
  Future<void> enforceDeviceLimit() async {
    try {
      // Aktuelle Anzahl aktiver Geräte abrufen
      int activeDevicesCount = await getActiveDevicesCount();
      
      // Aktuelles Abo abrufen
      SubscriptionPlan currentPlan = _subscriptionService.getCurrentPlan();
      
      // Wenn mehr Geräte aktiv sind als erlaubt
      if (activeDevicesCount > currentPlan.maxDevices) {
        debugPrint('DeviceServiceExtension: Zu viele aktive Geräte: '  
            '$activeDevicesCount aktiv, ${currentPlan.maxDevices} erlaubt. '  
            'Deaktiviere überzählige Geräte...');
        
        // Alle Geräte abrufen
        final devices = await getAllDevicesWithSubscriptionStatus();
        
        // Sortieren: Das aktuelle Gerät hat Priorität, dann nach Datum
        final currentDeviceId = (await getCurrentDevice())?.id;
        devices.sort((a, b) {
          // Aktuelles Gerät immer priorisieren
          if (a.id == currentDeviceId) return -1;
          if (b.id == currentDeviceId) return 1;
          
          // Ansonsten nach letztem Update-Datum sortieren (neuere zuerst)
          return b.updatedAt.compareTo(a.updatedAt);
        });
        
        // Die ersten currentPlan.maxDevices behalten, den Rest deaktivieren
        int deactivatedCount = 0;
        for (int i = 0; i < devices.length; i++) {
          if (i >= currentPlan.maxDevices && devices[i].isActive) {
            // Gerät deaktivieren
            await updateDeviceSubscriptionStatus(devices[i].id, false);
            deactivatedCount++;
          }
        }
        
        debugPrint('DeviceServiceExtension: $deactivatedCount überzählige Geräte deaktiviert.');
      }
    } catch (e) {
      debugPrint('DeviceServiceExtension: Fehler beim Durchsetzen des Gerätelimits: $e');
    }
  }

  /// Aktualisiert einen Gerätestatus basierend auf dem aktuellen Abonnement
  Future<bool> updateDeviceBasedOnSubscription(String deviceId, bool activate) async {
    try {
      // Aktuelle Anzahl aktiver Geräte abrufen
      int activeDevicesCount = await getActiveDevicesCount();

      // Aktuelles Abo abrufen
      SubscriptionPlan currentPlan = _subscriptionService.getCurrentPlan();

      // Prüfen, ob ein neues Gerät aktiviert werden kann
      if (activate) {
        // Bei Aktivierung: Prüfen, ob das Limit erreicht ist
        if (activeDevicesCount >= currentPlan.maxDevices) {
          debugPrint('DeviceServiceExtension: Abo-Limit erreicht (${currentPlan.type.displayName}): '
              'Aktive Geräte: $activeDevicesCount, Maximum: ${currentPlan.maxDevices}');
          return false;
        }
      }

      // Gerät aktualisieren
      return updateDeviceSubscriptionStatus(deviceId, activate);
    } catch (e) {
      debugPrint('DeviceServiceExtension: Fehler beim Aktualisieren des Geräts basierend auf Abo: $e');
      return false;
    }
  }

  /// Gibt zurück, ob weitere Geräte aktiviert werden können
  Future<bool> canActivateMoreDevices() async {
    try {
      int activeDevicesCount = await getActiveDevicesCount();
      SubscriptionPlan currentPlan = _subscriptionService.getCurrentPlan();
      
      return activeDevicesCount < currentPlan.maxDevices;
    } catch (e) {
      debugPrint('DeviceServiceExtension: Fehler beim Prüfen der aktivierbaren Geräte: $e');
      return false;
    }
  }

  /// Gibt das aktuelle Abonnement zurück
  SubscriptionPlan getCurrentSubscription() {
    return _subscriptionService.getCurrentPlan();
  }

  /// Gibt zurück, ob die tägliche Verkaufsgrenze bereits erreicht ist
  Future<bool> isWithinDailySalesLimit(int todaysSalesCount) async {
    return _subscriptionService.isWithinDailySalesLimit(todaysSalesCount);
  }

  /// Startet den Kauf eines Abonnements
  Future<bool> purchaseSubscription(SubscriptionType type) async {
    return _subscriptionService.purchaseSubscription(type);
  }

  /// Gibt alle verfügbaren Abonnement-Typen zurück
  List<SubscriptionType> getAllSubscriptionTypes() {
    return _subscriptionService.getAllSubscriptionTypes();
  }

  /// Gibt den aktuellen Abonnement-Typ zurück
  SubscriptionType getCurrentSubscriptionType() {
    return _subscriptionService.getCurrentPlan().type;
  }

  /// Setzt den Abonnement-Typ im Debug-Modus
  Future<void> setDebugSubscriptionType(SubscriptionType type) async {
    if (kDebugMode) {
      await _subscriptionService.setDebugSubscriptionPlan(type);
      
      // Nach Änderung des Abo-Typs sicherstellen, dass das Gerätelimit eingehalten wird
      await enforceDeviceLimit();
    }
  }
}
