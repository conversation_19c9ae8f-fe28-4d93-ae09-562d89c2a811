{"logs": [{"outputFile": "com.example.troedelpos.app-mergeDebugResources-55:/values-vi/values-vi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\596dc2cdab8b9d2ef83e7d6e0e134b8a\\transformed\\appcompat-1.6.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,423,507,610,729,807,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1904,2008,2116,2217,2322,2437,2542,2699,2798", "endColumns": "106,101,108,83,102,118,77,75,90,92,94,93,99,92,94,93,90,90,83,103,107,100,104,114,104,156,98,84", "endOffsets": "207,309,418,502,605,724,802,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1899,2003,2111,2212,2317,2432,2537,2694,2793,2878"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,423,507,610,729,807,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1904,2008,2116,2217,2322,2437,2542,2699,6824", "endColumns": "106,101,108,83,102,118,77,75,90,92,94,93,99,92,94,93,90,90,83,103,107,100,104,114,104,156,98,84", "endOffsets": "207,309,418,502,605,724,802,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1899,2003,2111,2212,2317,2432,2537,2694,2793,6904"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3f6219dea7a69dbc6d2bf4921723a7d5\\transformed\\browser-1.8.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,172,283,397", "endColumns": "116,110,113,110", "endOffsets": "167,278,392,503"}, "to": {"startLines": "57,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "6066,6270,6381,6495", "endColumns": "116,110,113,110", "endOffsets": "6178,6376,6490,6601"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a75011f7f88673231c312008696729f9\\transformed\\preference-1.2.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,264,339,482,651,731", "endColumns": "71,86,74,142,168,79,76", "endOffsets": "172,259,334,477,646,726,803"}, "to": {"startLines": "56,58,62,63,66,67,68", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5994,6183,6606,6681,7010,7179,7259", "endColumns": "71,86,74,142,168,79,76", "endOffsets": "6061,6265,6676,6819,7174,7254,7331"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\29b368049dbdfe7c192fa848707b37b6\\transformed\\jetified-play-services-base-18.5.0\\res\\values-vi\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,291,449,570,675,836,962,1077,1177,1346,1449,1602,1728,1883,2028,2092,2152", "endColumns": "97,157,120,104,160,125,114,99,168,102,152,125,154,144,63,59,78", "endOffsets": "290,448,569,674,835,961,1076,1176,1345,1448,1601,1727,1882,2027,2091,2151,2230"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3760,3862,4024,4149,4258,4423,4553,4672,4904,5077,5184,5341,5471,5630,5779,5847,5911", "endColumns": "101,161,124,108,164,129,118,103,172,106,156,129,158,148,67,63,82", "endOffsets": "3857,4019,4144,4253,4418,4548,4667,4771,5072,5179,5336,5466,5625,5774,5842,5906,5989"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\19d4c2635369e2aa123936ad1ab3a080\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-vi\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "123", "endOffsets": "318"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4776", "endColumns": "127", "endOffsets": "4899"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\62aeb653dda202444616439a5b81d504\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,119", "endOffsets": "162,282"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2798,2910", "endColumns": "111,119", "endOffsets": "2905,3025"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fe930d2507b0279f955567b6d5748a32\\transformed\\core-1.13.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,556,669,785", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "147,249,348,448,551,664,780,881"}, "to": {"startLines": "31,32,33,34,35,36,37,65", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3030,3127,3229,3328,3428,3531,3644,6909", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "3122,3224,3323,3423,3526,3639,3755,7005"}}]}]}