com.example.troedelpos.app-jetified-lifecycle-viewmodel-ktx-2.7.0-0 C:\Users\<USER>\.gradle\caches\transforms-3\02758c2639608ddeb41a1cc8871e22ca\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\res
com.example.troedelpos.app-jetified-core-1.0.0-1 C:\Users\<USER>\.gradle\caches\transforms-3\054c76b58a6a5d9ab83b41630a67375d\transformed\jetified-core-1.0.0\res
com.example.troedelpos.app-jetified-core-common-2.0.3-2 C:\Users\<USER>\.gradle\caches\transforms-3\17add4f136784dea2dd439689278c96a\transformed\jetified-core-common-2.0.3\res
com.example.troedelpos.app-jetified-play-services-basement-18.4.0-3 C:\Users\<USER>\.gradle\caches\transforms-3\19d4c2635369e2aa123936ad1ab3a080\transformed\jetified-play-services-basement-18.4.0\res
com.example.troedelpos.app-lifecycle-livedata-core-2.7.0-4 C:\Users\<USER>\.gradle\caches\transforms-3\22bdf8c02212a84b53c5394bd2b9a913\transformed\lifecycle-livedata-core-2.7.0\res
com.example.troedelpos.app-jetified-lifecycle-runtime-ktx-2.7.0-5 C:\Users\<USER>\.gradle\caches\transforms-3\2319c56b9cfd2a5b60e4b20ec5eb99ae\transformed\jetified-lifecycle-runtime-ktx-2.7.0\res
com.example.troedelpos.app-jetified-play-services-base-18.5.0-6 C:\Users\<USER>\.gradle\caches\transforms-3\29b368049dbdfe7c192fa848707b37b6\transformed\jetified-play-services-base-18.5.0\res
com.example.troedelpos.app-work-runtime-2.7.0-7 C:\Users\<USER>\.gradle\caches\transforms-3\29b5f0c3c2e71bfebc91f205e3c38906\transformed\work-runtime-2.7.0\res
com.example.troedelpos.app-lifecycle-livedata-2.7.0-8 C:\Users\<USER>\.gradle\caches\transforms-3\2f13277dadda22a5dec6af8e85ed61f5\transformed\lifecycle-livedata-2.7.0\res
com.example.troedelpos.app-jetified-billing-7.1.1-9 C:\Users\<USER>\.gradle\caches\transforms-3\2f79562eedfa06deadff6479d4f4acac\transformed\jetified-billing-7.1.1\res
com.example.troedelpos.app-jetified-firebase-common-21.0.0-10 C:\Users\<USER>\.gradle\caches\transforms-3\3bb114678107da1ef99f97798dc193bb\transformed\jetified-firebase-common-21.0.0\res
com.example.troedelpos.app-browser-1.8.0-11 C:\Users\<USER>\.gradle\caches\transforms-3\3f6219dea7a69dbc6d2bf4921723a7d5\transformed\browser-1.8.0\res
com.example.troedelpos.app-jetified-fragment-ktx-1.7.1-12 C:\Users\<USER>\.gradle\caches\transforms-3\413187884bd07825bbb7564f504ae288\transformed\jetified-fragment-ktx-1.7.1\res
com.example.troedelpos.app-coordinatorlayout-1.0.0-13 C:\Users\<USER>\.gradle\caches\transforms-3\42642eb3456df98d1184c1227b27f028\transformed\coordinatorlayout-1.0.0\res
com.example.troedelpos.app-jetified-appcompat-resources-1.6.1-14 C:\Users\<USER>\.gradle\caches\transforms-3\4595a3b296e32e7039e98543d2f65518\transformed\jetified-appcompat-resources-1.6.1\res
com.example.troedelpos.app-jetified-activity-1.8.1-15 C:\Users\<USER>\.gradle\caches\transforms-3\49f654a543c702b15417705ac1f9d737\transformed\jetified-activity-1.8.1\res
com.example.troedelpos.app-jetified-activity-ktx-1.8.1-16 C:\Users\<USER>\.gradle\caches\transforms-3\533b9bce64e47fda78cbe1baa0b6ca7d\transformed\jetified-activity-ktx-1.8.1\res
com.example.troedelpos.app-jetified-startup-runtime-1.1.1-17 C:\Users\<USER>\.gradle\caches\transforms-3\5366afd6a5dea0a6abfe5e8efa6b7106\transformed\jetified-startup-runtime-1.1.1\res
com.example.troedelpos.app-appcompat-1.6.1-18 C:\Users\<USER>\.gradle\caches\transforms-3\596dc2cdab8b9d2ef83e7d6e0e134b8a\transformed\appcompat-1.6.1\res
com.example.troedelpos.app-jetified-security-crypto-1.1.0-alpha06-19 C:\Users\<USER>\.gradle\caches\transforms-3\60d11028ed788bd90eba3bf1e273bf97\transformed\jetified-security-crypto-1.1.0-alpha06\res
com.example.troedelpos.app-jetified-credentials-1.2.0-rc01-20 C:\Users\<USER>\.gradle\caches\transforms-3\62aeb653dda202444616439a5b81d504\transformed\jetified-credentials-1.2.0-rc01\res
com.example.troedelpos.app-jetified-window-java-1.2.0-21 C:\Users\<USER>\.gradle\caches\transforms-3\636e11c147967ac5df8d2d450eee6ea7\transformed\jetified-window-java-1.2.0\res
com.example.troedelpos.app-slidingpanelayout-1.2.0-22 C:\Users\<USER>\.gradle\caches\transforms-3\6811d104c5a3aa633077b6f7528fd5be\transformed\slidingpanelayout-1.2.0\res
com.example.troedelpos.app-jetified-window-1.2.0-23 C:\Users\<USER>\.gradle\caches\transforms-3\69a6d671be6bdd1a1fdb40a1fed71722\transformed\jetified-window-1.2.0\res
com.example.troedelpos.app-jetified-savedstate-ktx-1.2.1-24 C:\Users\<USER>\.gradle\caches\transforms-3\6b3b87df348d283ed0d0177ddac4a209\transformed\jetified-savedstate-ktx-1.2.1\res
com.example.troedelpos.app-jetified-lifecycle-service-2.7.0-25 C:\Users\<USER>\.gradle\caches\transforms-3\7bd6cd68b2dfb6d856e0136c55cf13cf\transformed\jetified-lifecycle-service-2.7.0\res
com.example.troedelpos.app-jetified-credentials-play-services-auth-1.2.0-rc01-26 C:\Users\<USER>\.gradle\caches\transforms-3\811d866da11c4adc1e237c7be48ea17a\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\res
com.example.troedelpos.app-recyclerview-1.0.0-27 C:\Users\<USER>\.gradle\caches\transforms-3\88a95dc090f8ff91430f7b0681cf077e\transformed\recyclerview-1.0.0\res
com.example.troedelpos.app-jetified-digital-ink-recognition-18.1.0-28 C:\Users\<USER>\.gradle\caches\transforms-3\8fc52eb327e2202155f3dbc243d8187a\transformed\jetified-digital-ink-recognition-18.1.0\res
com.example.troedelpos.app-jetified-lifecycle-process-2.7.0-29 C:\Users\<USER>\.gradle\caches\transforms-3\952d74a4f84de990b1d8333e6126b61e\transformed\jetified-lifecycle-process-2.7.0\res
com.example.troedelpos.app-fragment-1.7.1-30 C:\Users\<USER>\.gradle\caches\transforms-3\9c73351b3eef23b386c6d565f7babcb3\transformed\fragment-1.7.1\res
com.example.troedelpos.app-jetified-annotation-experimental-1.4.0-31 C:\Users\<USER>\.gradle\caches\transforms-3\9c9b8e3e4f17d010e1c0e832e4e70c3a\transformed\jetified-annotation-experimental-1.4.0\res
com.example.troedelpos.app-lifecycle-runtime-2.7.0-32 C:\Users\<USER>\.gradle\caches\transforms-3\9da313400a33b9893d3dbfb49fa206ea\transformed\lifecycle-runtime-2.7.0\res
com.example.troedelpos.app-jetified-datastore-preferences-release-33 C:\Users\<USER>\.gradle\caches\transforms-3\a0be6dffea1cae977021334fd59ef10b\transformed\jetified-datastore-preferences-release\res
com.example.troedelpos.app-preference-1.2.1-34 C:\Users\<USER>\.gradle\caches\transforms-3\a75011f7f88673231c312008696729f9\transformed\preference-1.2.1\res
com.example.troedelpos.app-jetified-emoji2-views-helper-1.2.0-35 C:\Users\<USER>\.gradle\caches\transforms-3\aac14126756343a762a1dd95f0000a89\transformed\jetified-emoji2-views-helper-1.2.0\res
com.example.troedelpos.app-core-runtime-2.2.0-36 C:\Users\<USER>\.gradle\caches\transforms-3\ae15ae464d3f6a2bffa5ec2c13d9f7f0\transformed\core-runtime-2.2.0\res
com.example.troedelpos.app-jetified-datastore-release-37 C:\Users\<USER>\.gradle\caches\transforms-3\b5a5938ba4c337d2780ca6b2831f0454\transformed\jetified-datastore-release\res
com.example.troedelpos.app-jetified-datastore-core-release-38 C:\Users\<USER>\.gradle\caches\transforms-3\b6e6fee4870df8ecd684116bf49b75c8\transformed\jetified-datastore-core-release\res
com.example.troedelpos.app-jetified-profileinstaller-1.3.1-39 C:\Users\<USER>\.gradle\caches\transforms-3\bad1b5e8c5d27bc2276c2028ca982d0b\transformed\jetified-profileinstaller-1.3.1\res
com.example.troedelpos.app-transition-1.4.1-40 C:\Users\<USER>\.gradle\caches\transforms-3\bf696adcbb799744dfabaf7bcafd0c7b\transformed\transition-1.4.1\res
com.example.troedelpos.app-jetified-savedstate-1.2.1-41 C:\Users\<USER>\.gradle\caches\transforms-3\c1d441b924febfe273cac3f5a70e0a3e\transformed\jetified-savedstate-1.2.1\res
com.example.troedelpos.app-jetified-lifecycle-livedata-core-ktx-2.7.0-42 C:\Users\<USER>\.gradle\caches\transforms-3\c6e989c6c7df602195c3a4bfb2989f60\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\res
com.example.troedelpos.app-lifecycle-viewmodel-2.7.0-43 C:\Users\<USER>\.gradle\caches\transforms-3\c82c4d384cf906d14a42b3afe79264c6\transformed\lifecycle-viewmodel-2.7.0\res
com.example.troedelpos.app-jetified-emoji2-1.2.0-44 C:\Users\<USER>\.gradle\caches\transforms-3\cb4f9b7546ddd8ec180df336ad808fa4\transformed\jetified-emoji2-1.2.0\res
com.example.troedelpos.app-jetified-play-services-auth-21.0.0-45 C:\Users\<USER>\.gradle\caches\transforms-3\f3998f03731776e641b0412496162b18\transformed\jetified-play-services-auth-21.0.0\res
com.example.troedelpos.app-jetified-lifecycle-viewmodel-savedstate-2.7.0-46 C:\Users\<USER>\.gradle\caches\transforms-3\f4e96cff6687fdf30a110a5e18edf8ad\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\res
com.example.troedelpos.app-jetified-core-ktx-1.13.1-47 C:\Users\<USER>\.gradle\caches\transforms-3\f6c9548f82100dc94f1188394ca35efd\transformed\jetified-core-ktx-1.13.1\res
com.example.troedelpos.app-jetified-tracing-1.2.0-48 C:\Users\<USER>\.gradle\caches\transforms-3\fddaf1edf19f323b6cf7e30f79481aa5\transformed\jetified-tracing-1.2.0\res
com.example.troedelpos.app-core-1.13.1-49 C:\Users\<USER>\.gradle\caches\transforms-3\fe930d2507b0279f955567b6d5748a32\transformed\core-1.13.1\res
com.example.troedelpos.app-debug-50 C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\debug\res
com.example.troedelpos.app-main-51 C:\Users\<USER>\Documents\environments\troedelpos\android\app\src\main\res
com.example.troedelpos.app-pngs-52 C:\Users\<USER>\Documents\environments\troedelpos\build\app\generated\res\pngs\debug
com.example.troedelpos.app-res-53 C:\Users\<USER>\Documents\environments\troedelpos\build\app\generated\res\processDebugGoogleServices
com.example.troedelpos.app-resValues-54 C:\Users\<USER>\Documents\environments\troedelpos\build\app\generated\res\resValues\debug
com.example.troedelpos.app-packageDebugResources-55 C:\Users\<USER>\Documents\environments\troedelpos\build\app\intermediates\incremental\debug\packageDebugResources\merged.dir
com.example.troedelpos.app-packageDebugResources-56 C:\Users\<USER>\Documents\environments\troedelpos\build\app\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.example.troedelpos.app-merged_res-57 C:\Users\<USER>\Documents\environments\troedelpos\build\app\intermediates\merged_res\debug
com.example.troedelpos.app-packaged_res-58 C:\Users\<USER>\Documents\environments\troedelpos\build\camera_android\intermediates\packaged_res\debug
com.example.troedelpos.app-packaged_res-59 C:\Users\<USER>\Documents\environments\troedelpos\build\cloud_firestore\intermediates\packaged_res\debug
com.example.troedelpos.app-packaged_res-60 C:\Users\<USER>\Documents\environments\troedelpos\build\connectivity_plus\intermediates\packaged_res\debug
com.example.troedelpos.app-packaged_res-61 C:\Users\<USER>\Documents\environments\troedelpos\build\firebase_auth\intermediates\packaged_res\debug
com.example.troedelpos.app-packaged_res-62 C:\Users\<USER>\Documents\environments\troedelpos\build\firebase_core\intermediates\packaged_res\debug
com.example.troedelpos.app-packaged_res-63 C:\Users\<USER>\Documents\environments\troedelpos\build\flutter_plugin_android_lifecycle\intermediates\packaged_res\debug
com.example.troedelpos.app-packaged_res-64 C:\Users\<USER>\Documents\environments\troedelpos\build\flutter_secure_storage\intermediates\packaged_res\debug
com.example.troedelpos.app-packaged_res-65 C:\Users\<USER>\Documents\environments\troedelpos\build\google_mlkit_commons\intermediates\packaged_res\debug
com.example.troedelpos.app-packaged_res-66 C:\Users\<USER>\Documents\environments\troedelpos\build\google_mlkit_digital_ink_recognition\intermediates\packaged_res\debug
com.example.troedelpos.app-packaged_res-67 C:\Users\<USER>\Documents\environments\troedelpos\build\google_mlkit_text_recognition\intermediates\packaged_res\debug
com.example.troedelpos.app-packaged_res-68 C:\Users\<USER>\Documents\environments\troedelpos\build\google_sign_in_android\intermediates\packaged_res\debug
com.example.troedelpos.app-packaged_res-69 C:\Users\<USER>\Documents\environments\troedelpos\build\in_app_purchase_android\intermediates\packaged_res\debug
com.example.troedelpos.app-packaged_res-70 C:\Users\<USER>\Documents\environments\troedelpos\build\path_provider_android\intermediates\packaged_res\debug
com.example.troedelpos.app-packaged_res-71 C:\Users\<USER>\Documents\environments\troedelpos\build\permission_handler_android\intermediates\packaged_res\debug
com.example.troedelpos.app-packaged_res-72 C:\Users\<USER>\Documents\environments\troedelpos\build\share_plus\intermediates\packaged_res\debug
com.example.troedelpos.app-packaged_res-73 C:\Users\<USER>\Documents\environments\troedelpos\build\shared_preferences_android\intermediates\packaged_res\debug
com.example.troedelpos.app-packaged_res-74 C:\Users\<USER>\Documents\environments\troedelpos\build\sqflite_android\intermediates\packaged_res\debug
com.example.troedelpos.app-packaged_res-75 C:\Users\<USER>\Documents\environments\troedelpos\build\url_launcher_android\intermediates\packaged_res\debug
