import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

/// Einfacher Debug-Bildschirm, um den Status des Test-Accounts zu prüfen
class DebugTestAccountScreen extends StatefulWidget {
  const DebugTestAccountScreen({Key? key}) : super(key: key);

  @override
  _DebugTestAccountScreenState createState() => _DebugTestAccountScreenState();
}

class _DebugTestAccountScreenState extends State<DebugTestAccountScreen> {
  bool _isLoading = true;
  String _userId = '';
  String _userEmail = '';
  bool _isTestAccountById = false;
  bool _isTestAccountByEmail = false;
  String _errorMessage = '';
  
  @override
  void initState() {
    super.initState();
    _checkTestAccountStatus();
  }
  
  Future<void> _checkTestAccountStatus() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });
    
    try {
      // Aktuelle Benutzerinformationen abrufen
      final User? currentUser = FirebaseAuth.instance.currentUser;
      
      if (currentUser == null) {
        setState(() {
          _errorMessage = 'Kein angemeldeter Benutzer gefunden.';
          _isLoading = false;
        });
        return;
      }
      
      _userId = currentUser.uid;
      _userEmail = currentUser.email ?? 'Keine E-Mail verfügbar';
      
      // 1. Prüfen, ob die UID in der tester_accounts-Sammlung ist
      final docById = await FirebaseFirestore.instance
          .collection('tester_accounts')
          .doc(_userId)
          .get();
      
      _isTestAccountById = docById.exists;
      
      // 2. Prüfen, ob die E-Mail in der tester_accounts-Sammlung ist
      if (_userEmail.isNotEmpty) {
        final docByEmail = await FirebaseFirestore.instance
            .collection('tester_accounts')
            .doc(_userEmail)
            .get();
        
        _isTestAccountByEmail = docByEmail.exists;
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Fehler bei der Überprüfung: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Test-Account Debug'),
      ),
      body: _isLoading
          ? Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Benutzerinformationen
                  Card(
                    margin: EdgeInsets.only(bottom: 16.0),
                    child: Padding(
                      padding: EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('Benutzerinformationen',
                              style: Theme.of(context).textTheme.titleLarge),
                          SizedBox(height: 8.0),
                          Text('Benutzer-ID: $_userId'),
                          Text('E-Mail: $_userEmail'),
                        ],
                      ),
                    ),
                  ),
                  
                  // Test-Account-Status
                  Card(
                    margin: EdgeInsets.only(bottom: 16.0),
                    child: Padding(
                      padding: EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('Test-Account-Status',
                              style: Theme.of(context).textTheme.titleLarge),
                          SizedBox(height: 8.0),
                          _buildStatusRow(
                            'Nach ID in tester_accounts:',
                            _isTestAccountById,
                          ),
                          _buildStatusRow(
                            'Nach E-Mail in tester_accounts:',
                            _isTestAccountByEmail,
                          ),
                          _buildStatusRow(
                            'Insgesamt als Tester erkannt:',
                            _isTestAccountById || _isTestAccountByEmail,
                          ),
                        ],
                      ),
                    ),
                  ),
                  
                  // Fehlermeldung
                  if (_errorMessage.isNotEmpty)
                    Card(
                      color: Colors.red.shade50,
                      margin: EdgeInsets.only(bottom: 16.0),
                      child: Padding(
                        padding: EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('Fehler',
                                style: TextStyle(
                                  color: Colors.red.shade900,
                                  fontWeight: FontWeight.bold,
                                )),
                            SizedBox(height: 8.0),
                            Text(_errorMessage,
                                style: TextStyle(color: Colors.red.shade900)),
                          ],
                        ),
                      ),
                    ),
                  
                  // Aktionsbuttons
                  SizedBox(height: 16.0),
                  ElevatedButton(
                    onPressed: _checkTestAccountStatus,
                    child: Text('Status erneut prüfen'),
                  ),
                ],
              ),
            ),
    );
  }
  
  Widget _buildStatusRow(String label, bool status) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          Expanded(
            child: Text(label),
          ),
          Icon(
            status ? Icons.check_circle : Icons.cancel,
            color: status ? Colors.green : Colors.red,
          ),
          SizedBox(width: 8.0),
          Text(
            status ? 'Ja' : 'Nein',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: status ? Colors.green : Colors.red,
            ),
          ),
        ],
      ),
    );
  }
}
