import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart'; // Import Firebase Auth (gute Praxis)
import 'package:provider/provider.dart';

// Screens
import 'screens/home_screen.dart';
import 'screens/profile_screen.dart';
import 'screens/vendor_screen.dart';
import 'screens/cashier_screen.dart';
import 'screens/flea_market_screen.dart';
import 'screens/flea_market_analysis_screen.dart';
import 'screens/journal_screen.dart';
import 'screens/device_naming_screen.dart';
import 'screens/settings_screen.dart';
import 'screens/onboarding_screen.dart';
import 'screens/subscription_screen.dart';
import 'services/onboarding_service.dart';
import 'widgets/showcase_helper.dart';

// Services
import 'services/session_service.dart';
import 'services/database_service.dart';
import 'services/device_service.dart';
import 'services/sale_service.dart';
import 'services/flea_market_service.dart'
    as services; // Alias für FleaMarketService
import 'services/connectivity_service.dart';

// Models
import 'models/device.dart';

// Utils
import 'utils/app_theme.dart';

void main() async {
  // Sicherstellen, dass Flutter initialisiert ist
  WidgetsFlutterBinding.ensureInitialized();

  // Firebase initialisieren
  await Firebase.initializeApp(
      // Wenn Sie FlutterFire CLI nutzen:
      // options: DefaultFirebaseOptions.currentPlatform,
      );

  // Bevorzugte Ausrichtung auf Querformat setzen
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.landscapeLeft,
    DeviceOrientation.landscapeRight,
  ]);

  // Statusleiste und Navigationsleiste konfigurieren:
  // 1. Aktiviere beide Leisten
  SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);

  // 2. Mache die Statusleiste transparent, damit die App-Farbe durchscheint
  SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
    statusBarColor: Colors.transparent, // Transparent für App-Farbe
    statusBarIconBrightness: Brightness.light, // Für dunkle App-Farben
  ));

  // Datenbank-Service initialisieren
  final databaseService = DatabaseService();
  await databaseService
      .database; // Stellt sicher, dass onCreate/onUpgrade läuft
  debugPrint("main: Lokale Datenbank initialisiert/geöffnet.");

  // Session-Service initialisieren (behandelt jetzt Firebase Auth Status)
  final sessionService = SessionService();
  // Initialisierung des Listeners + Laden des initialen Status erfolgt im SessionService selbst

  // Device Service initialisieren
  final deviceService = DeviceService();

  // ConnectivityService initialisieren, aber erst nach dem Start und erfolgreichem Login/Device Check
  final connectivityService = ConnectivityService();

  runApp(MyApp(
      sessionService: sessionService,
      deviceService: deviceService,
      databaseService: databaseService,
      connectivityService: connectivityService));
}

class MyApp extends StatefulWidget {
  final SessionService sessionService;
  final DeviceService deviceService;
  final DatabaseService databaseService;
  final ConnectivityService connectivityService;

  MyApp({
    required this.sessionService,
    required this.deviceService,
    required this.databaseService,
    required this.connectivityService,
    Key? key,
  }) : super(key: key);

  @override
  _MyAppState createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  late SessionService _sessionService;
  late DeviceService _deviceService;
  late DatabaseService _databaseService;
  late ConnectivityService _connectivityService;

  // Status für die initiale Prüfung (Login + Device)
  bool _isInitialCheckComplete = false;
  bool _isAuthenticated = false; // Wird durch Listener aktualisiert
  bool _hasActiveDevice = false; // NEU: Status für aktives Gerät
  bool _isOnboardingPhase1Completed = false; // Status für Onboarding Phase 1

  @override
  void initState() {
    super.initState();
    _sessionService = widget.sessionService;
    _deviceService = widget.deviceService;
    _databaseService = widget.databaseService;
    _connectivityService = widget.connectivityService;

    // Onboarding-Status laden
    _loadOnboardingStatus();

    // Initialisiere den SessionService (startet den Auth Listener)
    // und DANN prüfe den initialen Status.
    _sessionService.initialize().then((_) {
      // Höre auf Änderungen im Authentifizierungsstatus vom SessionService Notifier
      _sessionService.authStateNotifier.addListener(_onAuthStateChanged);

      // Starte die initiale Prüfung, NACHDEM der Listener im SessionService aktiv ist
      _checkAuthAndDeviceStatus();
    });
  }

  /// Lädt den Onboarding-Status aus den SharedPreferences
  Future<void> _loadOnboardingStatus() async {
    final onboardingService = OnboardingService();
    _isOnboardingPhase1Completed = await onboardingService.isPhase1Completed();
    debugPrint(
        "main.dart/_loadOnboardingStatus: Phase 1 completed = $_isOnboardingPhase1Completed");
  }

  @override
  void dispose() {
    // Listener entfernen
    _sessionService.authStateNotifier.removeListener(_onAuthStateChanged);
    // SessionService und ConnectivityService sollten ggf. eigene dispose-Methoden haben
    // _sessionService.dispose(); // Wenn im SessionService implementiert
    // _connectivityService.dispose(); // Wenn im ConnectivityService implementiert
    super.dispose();
  }

  // Wird aufgerufen, wenn sich der Login-Status (Firebase Auth) ändert
  void _onAuthStateChanged() {
    // Wenn sich der Auth-Status ändert, muss die Prüfung neu laufen
    debugPrint(
        "main.dart/_MyAppState: Auth state changed notification received.");
    _checkAuthAndDeviceStatus();
  }

  /// Prüft Login-Status (jetzt Firebase Auth) und leitet zum DeviceNamingScreen weiter
  Future<void> _checkAuthAndDeviceStatus() async {
    if (mounted) {
      setState(() {
        _isInitialCheckComplete = false;
        _hasActiveDevice = false; // Status zurücksetzen
      });
    }

    // Hole den aktuellen Auth-Status vom SessionService (der Firebase Auth prüft)
    bool authStatus = _sessionService.isAuthenticated;
    debugPrint(
        "main.dart/_checkAuthAndDeviceStatus: isAuthenticated = $authStatus");

    if (authStatus) {
      try {
        // Prüfe, ob das aktuelle Gerät aktiv ist
        Device? currentDevice = await _deviceService.getCurrentDevice();
        bool isDeviceActive = currentDevice?.isActive ?? false;

        debugPrint(
            "main.dart/_checkAuthAndDeviceStatus: Device active check: isDeviceActive=$isDeviceActive");

        // Wenn aktiviert ist, zusätzliche Schritte ausführen
        if (isDeviceActive) {
          debugPrint(
              "main.dart/_checkAuthAndDeviceStatus: Starte Datenbankprüfung für aktives Gerät");
          await _databaseService.ensureDatabaseStructure();

          // Aktive Märkte abrufen
          final fleaMarketService = services.FleaMarketService();
          final activeMarkets = await fleaMarketService.getActiveFleaMarkets();

          if (activeMarkets.isEmpty) {
            debugPrint(
                "main.dart/_checkAuthAndDeviceStatus: Keine aktiven Märkte gefunden.");
          } else {
            // Verkäufe für aktive Märkte synchronisieren
            final saleService = SaleService();
            for (final market in activeMarkets) {
              debugPrint(
                  "main.dart/_checkAuthAndDeviceStatus: Lade Verkäufe für Markt: ${market.name}");
              await saleService.getSalesForFleaMarket(market.id);
            }
            debugPrint(
                "main.dart/_checkAuthAndDeviceStatus: Verkaufsdaten für aktive Märkte geladen.");
          }

          // ConnectivityService initialisieren
          await _connectivityService.initialize();
          debugPrint("main: ConnectivityService initialisiert.");

          // Status für aktives Gerät setzen
          if (mounted) {
            setState(() {
              _hasActiveDevice = true;
            });
          }
        }
      } catch (e, s) {
        debugPrint(
            "main.dart/_checkAuthAndDeviceStatus: Fehler bei der Geräte- oder Datenprüfung: $e\n$s");
      }
    } else {
      debugPrint("main.dart/_checkAuthAndDeviceStatus: Nicht authentifiziert.");
    }

    // Zustand aktualisieren und UI neu bauen
    if (mounted) {
      setState(() {
        _isAuthenticated = authStatus;
        _isInitialCheckComplete = true;
      });
      debugPrint(
          "main.dart/_MyAppState: Check complete. Auth: $_isAuthenticated, HasActiveDevice: $_hasActiveDevice");
    }
  }

  Widget _buildInitialLoading() {
    return const Scaffold(
      body: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text("App wird gestartet..."),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    Widget homeWidget;

    if (!_isInitialCheckComplete) {
      homeWidget = _buildInitialLoading();
    } else if (!_isOnboardingPhase1Completed || !_isAuthenticated) {
      // Onboarding zeigen, wenn Phase 1 nicht abgeschlossen ODER User nicht angemeldet
      debugPrint(
          "main.dart: Onboarding Phase 1 nicht abgeschlossen oder nicht angemeldet, zeige OnboardingScreen");
      homeWidget = OnboardingScreen();
    } else if (_hasActiveDevice) {
      // Wenn ein aktives Gerät vorhanden ist, direkt zum HomeScreen
      debugPrint("main.dart: Aktives Gerät vorhanden, zeige HomeScreen");
      homeWidget = HomeScreen();
    } else {
      // Nur wenn kein aktives Gerät gefunden wurde, zum DeviceNamingScreen
      debugPrint(
          "main.dart: Kein aktives Gerät gefunden, zeige DeviceNamingScreen");
      homeWidget = DeviceNamingScreen();
    }

    debugPrint(
        "main.dart/_MyAppState build(): HomeWidget type = ${homeWidget.runtimeType}");

    // ConnectivityService als Provider bereitstellen
    return MultiProvider(
      providers: [
        Provider<ConnectivityService>.value(value: _connectivityService),
        // Weitere globale Provider hier hinzufügen
      ],
      child: ShowcaseHelper.buildShowCaseWidget(
        child: MaterialApp(
          title: 'Kassenbuch für Trödelmärkte',
          theme: AppTheme.lightTheme,
          debugShowCheckedModeBanner: false,
          // GEÄNDERT: Angepasster Builder für bessere Systemleisten-Integration
          builder: (context, child) {
            // Angepasste SafeArea, die nur unten Platz lässt
            return Padding(
              // Nur unten Padding hinzufügen, nicht an der Statusleiste
              padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).padding.bottom,
              ),
              child: child ?? Container(),
            );
          },
          home: homeWidget, // Dynamische Startseite
          routes: {
            // Routen Definitionen
            '/home': (context) => HomeScreen(),
            '/name_device': (context) => DeviceNamingScreen(),
            '/profile': (context) => ProfileScreen(),
            '/vendors': (context) => VendorScreen(),
            '/cashiers': (context) => CashierScreen(),
            '/flea_markets': (context) => FleaMarketScreen(),
            '/flea_market_analysis': (context) => FleaMarketAnalysisScreen(),
            '/journal': (context) => JournalScreen(),
            '/settings': (context) => SettingsScreen(),
            '/onboarding': (context) => OnboardingScreen(),
            '/subscription': (context) => SubscriptionScreen(),
          },
        ),
      ),
    );
  }
}
