import 'package:flutter/material.dart';
import '../models/subscription_plan.dart';
import '../services/device_service.dart';
import '../services/device_service_extension.dart';

/// Debug-Helfer für die Entwicklung des Abonnement-Systems
/// Diese Klasse wird nur im Debug-Modus verwendet und ermöglicht das
/// einfache Testen verschiedener Abonnement-Typen
class SubscriptionDebugHelper extends StatefulWidget {
  const SubscriptionDebugHelper({Key? key}) : super(key: key);

  @override
  _SubscriptionDebugHelperState createState() => _SubscriptionDebugHelperState();
}

class _SubscriptionDebugHelperState extends State<SubscriptionDebugHelper> {
  final DeviceService _deviceService = DeviceService();
  SubscriptionType _selectedType = SubscriptionType.free;
  String _statusMessage = '';
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadCurrentSubscription();
  }

  Future<void> _loadCurrentSubscription() async {
    setState(() {
      _isLoading = true;
    });

    try {
      _selectedType = _deviceService.getCurrentSubscriptionType();
    } catch (e) {
      _statusMessage = 'Fehler beim Laden des Abos: $e';
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _changeSubscriptionType(SubscriptionType type) async {
    setState(() {
      _isLoading = true;
      _statusMessage = '';
    });

    try {
      await _deviceService.setDebugSubscriptionType(type);
      _selectedType = type;
      _statusMessage = 'Abonnement erfolgreich auf ${type.displayName} geändert!';
    } catch (e) {
      _statusMessage = 'Fehler beim Ändern des Abos: $e';
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      margin: const EdgeInsets.all(16.0),
      elevation: 4.0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
        side: BorderSide(
          color: theme.colorScheme.primary.withOpacity(0.5),
          width: 1.0,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Titel
            Row(
              children: [
                Icon(
                  Icons.developer_mode,
                  color: theme.colorScheme.primary,
                  size: 24.0,
                ),
                const SizedBox(width: 8.0),
                Text(
                  'Debug-Modus für Abonnements',
                  style: theme.textTheme.titleLarge?.copyWith(
                    color: theme.colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16.0),
            
            Text(
              'Aktuelles Abonnement: ${_selectedType.displayName}',
              style: theme.textTheme.titleMedium,
            ),
            
            const SizedBox(height: 16.0),
            
            Text(
              'Wähle ein Abonnement für Testzwecke:',
              style: theme.textTheme.bodyLarge,
            ),
            
            const SizedBox(height: 8.0),
            
            if (_isLoading)
              const Center(child: CircularProgressIndicator())
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: SubscriptionType.values.length,
                itemBuilder: (context, index) {
                  final type = SubscriptionType.values[index];
                  final isSelected = type == _selectedType;
                  
                  return ListTile(
                    title: Text(type.displayName),
                    subtitle: Text(type.description),
                    leading: Radio<SubscriptionType>(
                      value: type,
                      groupValue: _selectedType,
                      onChanged: (value) {
                        if (value != null) {
                          _changeSubscriptionType(value);
                        }
                      },
                    ),
                    selected: isSelected,
                    selectedTileColor: isSelected
                        ? theme.colorScheme.primary.withOpacity(0.1)
                        : null,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                  );
                },
              ),
            
            if (_statusMessage.isNotEmpty) ...[
              const SizedBox(height: 16.0),
              Container(
                padding: const EdgeInsets.all(8.0),
                decoration: BoxDecoration(
                  color: _statusMessage.contains('Fehler')
                      ? Colors.red.withOpacity(0.1)
                      : Colors.green.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8.0),
                ),
                child: Text(
                  _statusMessage,
                  style: TextStyle(
                    color: _statusMessage.contains('Fehler')
                        ? Colors.red.shade700
                        : Colors.green.shade700,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
