import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:showcaseview/showcaseview.dart';
import '../widgets/drawer_menu.dart';
import '../widgets/showcase_helper.dart';
import '../services/product_category_service.dart';
import '../services/onboarding_service.dart';
import '../models/product_category.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/device_service.dart'; // Importiert für Geräteverwaltung
import 'settings_screen_onboarding.dart';
import 'package:url_launcher/url_launcher.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({Key? key}) : super(key: key);

  @override
  _SettingsScreenState createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final ProductCategoryService _productCategoryService =
      ProductCategoryService();
  final DeviceService _deviceService = DeviceService(); // Neu: DeviceService
  final OnboardingService _onboardingService =
      OnboardingService(); // Für Onboarding-Reset
  late SettingsScreenOnboarding _onboarding;

  List<ProductCategory> _categories = [];
  bool _isLoadingCategories = true;
  bool _useCategoriesFeature = false;
  bool _isFullscreenEnabled = false;

  // NEU: State für Expander
  bool _isCategoriesSectionExpanded = false;

  // Für Erweiterte Optionen
  bool _isAdvancedOptionsExpanded = false;

  // Für Onboarding
  final GlobalKey _categoriesSwitchKey = GlobalKey();
  final GlobalKey _categoriesListKey = GlobalKey();
  final GlobalKey _onboardingResetKey = GlobalKey();

  // Konstanten für Shared Preferences Schlüssel
  static const String _useCategoriesKey = 'use_product_categories';
  static const String _useFullscreenKey = 'use_fullscreen_mode';

  @override
  void initState() {
    super.initState();
    _loadSettingsAndCategories();

    // Onboarding initialisieren
    _onboarding = SettingsScreenOnboarding(context: context);

    // Onboarding starten (nach kurzem Delay, damit die UI vollständig geladen ist)
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _onboarding.startOnboarding();
    });
  }

  // Zeigt das Modal für Erweiterte Optionen
  void _showAdvancedOptionsModal(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Indikator am oberen Rand
            Container(
              width: 40,
              height: 4,
              margin: EdgeInsets.only(bottom: 16),
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Text(
              'Erweiterte Optionen',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 20),
            // Datenschutzrichtlinie
            ListTile(
              leading: Icon(Icons.privacy_tip_outlined,
                  color: Theme.of(context).colorScheme.primary),
              title: Text('Datenschutzrichtlinie'),
              trailing: Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () async {
                Navigator.pop(context);
                const String urlString =
                    'https://sites.google.com/view/famigoapps/vendea-fleamarket-pos/privacy-policy-of-vendea';
                try {
                  final Uri url = Uri.parse(urlString);
                  await launchUrl(
                    url,
                    mode: LaunchMode.inAppWebView,
                    webViewConfiguration: const WebViewConfiguration(
                      enableJavaScript: true,
                      enableDomStorage: true,
                    ),
                  );
                } catch (e) {
                  debugPrint('Fehler beim Öffnen der URL: $e');
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                          content: Text(
                              'Konnte Datenschutzrichtlinie nicht öffnen: $e')),
                    );
                  }
                }
              },
            ),
            // Nutzungsbedingungen
            ListTile(
              leading: Icon(Icons.description_outlined,
                  color: Theme.of(context).colorScheme.primary),
              title: Text('Nutzungsbedingungen'),
              trailing: Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () async {
                Navigator.pop(context);
                const String urlString =
                    'https://sites.google.com/view/famigoapps/vendea-fleamarket-pos/terms-of-service-vendea';
                try {
                  final Uri url = Uri.parse(urlString);
                  await launchUrl(
                    url,
                    mode: LaunchMode.inAppWebView,
                    webViewConfiguration: const WebViewConfiguration(
                      enableJavaScript: true,
                      enableDomStorage: true,
                    ),
                  );
                } catch (e) {
                  debugPrint('Fehler beim Öffnen der URL: $e');
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                          content: Text(
                              'Konnte Nutzungsbedingungen nicht öffnen: $e')),
                    );
                  }
                }
              },
            ),
            // Open Source Lizenzen
            ListTile(
              leading: Icon(Icons.code_outlined,
                  color: Theme.of(context).colorScheme.primary),
              title: Text('Open Source Lizenzen'),
              trailing: Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () {
                Navigator.pop(context);
                _showLicensesModal(context);
              },
            ),
            SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  // Datenschutzrichtlinie und Nutzungsbedingungen wurden entfernt, da sie nun auf externe URLs verlinken

  // Zeigt die Open Source Lizenzen
  void _showLicensesModal(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: EdgeInsets.all(16),
        height: MediaQuery.of(context).size.height * 0.8,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Indikator am oberen Rand
            Center(
              child: Container(
                width: 40,
                height: 4,
                margin: EdgeInsets.only(bottom: 16),
                decoration: BoxDecoration(
                  color: Colors.grey.shade300,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            ),
            Text(
              'Open Source Lizenzen',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 16),
            Expanded(
              child: ListView(
                children: [
                  _licenseTile(
                    'Flutter',
                    'Google',
                    'BSD',
                    'https://opensource.org/licenses/BSD-3-Clause',
                  ),
                  _licenseTile(
                    'cupertino_icons',
                    'Flutter Team',
                    'MIT',
                    'https://opensource.org/licenses/MIT',
                  ),
                  _licenseTile(
                    'provider',
                    'Remi Rousselet',
                    'MIT',
                    'https://opensource.org/licenses/MIT',
                  ),
                  _licenseTile(
                    'shared_preferences',
                    'Flutter Team',
                    'BSD',
                    'https://opensource.org/licenses/BSD-3-Clause',
                  ),
                  _licenseTile(
                    'intl',
                    'Dart Team',
                    'MIT',
                    'https://opensource.org/licenses/MIT',
                  ),
                  _licenseTile(
                    'hive',
                    'Simon Leier',
                    'Apache 2.0',
                    'https://opensource.org/licenses/Apache-2.0',
                  ),
                  _licenseTile(
                    'url_launcher',
                    'Flutter Team',
                    'BSD',
                    'https://opensource.org/licenses/BSD-3-Clause',
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Hilfsfunktion für Lizenzen
  Widget _licenseTile(String packageName, String author, String licenseType,
      String licenseUrl) {
    return ListTile(
      title: Text(packageName),
      subtitle: Text(author),
      trailing: Text(licenseType),
      onTap: () {
        _showLicenseDetails(context, packageName, licenseUrl);
      },
    );
  }

  // Zeigt die Details einer Lizenz
  void _showLicenseDetails(
      BuildContext context, String packageName, String licenseUrl) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: EdgeInsets.all(16),
        height: MediaQuery.of(context).size.height * 0.6,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Indikator am oberen Rand
            Center(
              child: Container(
                width: 40,
                height: 4,
                margin: EdgeInsets.only(bottom: 16),
                decoration: BoxDecoration(
                  color: Colors.grey.shade300,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            ),
            Text(
              'Platzhalter für $packageName Lizenztext',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 16),
            Text(
              'Vollständiger Lizenztext: $licenseUrl',
              style: TextStyle(fontSize: 16),
            ),
            SizedBox(height: 16),
            Expanded(
              child: SingleChildScrollView(
                child: Text(
                  'Dies ist ein Platzhalter für den vollständigen Lizenztext. '
                  'Der tatsächliche Text würde die vollständigen Lizenzbedingungen enthalten.',
                  style: TextStyle(fontSize: 16),
                ),
              ),
            ),
            SizedBox(height: 16),
            ElevatedButton(
              onPressed: () async {
                final Uri url = Uri.parse(licenseUrl);
                if (await canLaunchUrl(url)) {
                  await launchUrl(url);
                } else {
                  throw 'Konnte URL nicht öffnen: $url';
                }
              },
              child: Text('Lizenz im Browser öffnen'),
            ),
          ],
        ),
      ),
    );
  }

  // Sequentielles Laden der Einstellungen und Kategorien
  Future<void> _loadSettingsAndCategories() async {
    await _loadSettings();
    if (_useCategoriesFeature) {
      await _loadCategories();
    }
  }

  // Lade die Einstellungen aus SharedPreferences
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final useCategories = prefs.getBool(_useCategoriesKey) ?? false;
      final useFullscreen = prefs.getBool(_useFullscreenKey) ?? false;

      if (mounted) {
        setState(() {
          _useCategoriesFeature = useCategories;
          _isFullscreenEnabled = useFullscreen;
        });
      }

      // Fullscreen-Modus anwenden
      _applyFullscreenMode();

      debugPrint(
          'Settings geladen: Warengruppen aktiviert: $_useCategoriesFeature, Vollbild: $_isFullscreenEnabled');
    } catch (e) {
      debugPrint('Fehler beim Laden der Einstellungen: $e');
      // Fallback: Features deaktiviert
      if (mounted) {
        setState(() {
          _useCategoriesFeature = false;
          _isFullscreenEnabled = false;
        });
      }
    }
  }

  // Speichere die Einstellungen in SharedPreferences
  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_useCategoriesKey, _useCategoriesFeature);
      await prefs.setBool(_useFullscreenKey, _isFullscreenEnabled);
      debugPrint(
          'Einstellungen gespeichert: Warengruppen: $_useCategoriesFeature, Vollbild: $_isFullscreenEnabled');
    } catch (e) {
      debugPrint('Fehler beim Speichern der Einstellungen: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Fehler beim Speichern der Einstellungen: $e')),
      );
    }
  }

  // NEU: Anwenden des Fullscreen-Modus
  void _applyFullscreenMode() {
    if (_isFullscreenEnabled) {
      // Immersive Sticky Mode mit Berücksichtigung der unteren Navigationsleiste
      SystemChrome.setEnabledSystemUIMode(
        SystemUiMode.immersiveSticky,
        overlays: [], // Keine Overlays im Fullscreen-Modus
      );
    } else {
      // Standard-Modus mit Berücksichtigung aller Systemleisten
      SystemChrome.setEnabledSystemUIMode(
        SystemUiMode.edgeToEdge,
        overlays: [SystemUiOverlay.top, SystemUiOverlay.bottom],
      );
    }
  }

  // Zurücksetzen des Onboardings
  Future<void> _resetOnboarding() async {
    try {
      // Bestätigungsdialog anzeigen
      final bool? confirm = await showDialog<bool>(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('Onboarding zurücksetzen?'),
            content: const Text(
                'Möchten Sie das Onboarding wirklich zurücksetzen? '
                'Beim nächsten Start der App werden alle Einführungen erneut angezeigt.'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Abbrechen'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Zurücksetzen'),
              ),
            ],
          );
        },
      );

      // Wenn der Benutzer bestätigt hat
      if (confirm == true) {
        // Onboarding zurücksetzen
        final success = await _onboardingService.resetAllOnboardingStatus();

        if (mounted) {
          if (success) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text(
                    'Onboarding wurde zurückgesetzt. Starten Sie die App neu, um die Einführung zu sehen.'),
                duration: Duration(seconds: 3),
              ),
            );
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Fehler beim Zurücksetzen des Onboardings.'),
                duration: Duration(seconds: 2),
              ),
            );
          }
        }
      }
    } catch (e) {
      debugPrint('Fehler beim Zurücksetzen des Onboardings: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Fehler: $e')),
        );
      }
    }
  }

  // Lade alle Warengruppen
  Future<void> _loadCategories() async {
    if (!_useCategoriesFeature) return;

    if (mounted) {
      setState(() => _isLoadingCategories = true);
    }

    try {
      final categories = await _productCategoryService.getAllCategories();

      if (mounted) {
        setState(() {
          _categories = categories;
          _isLoadingCategories = false;
        });
        debugPrint('${categories.length} Warengruppen erfolgreich geladen');
      }
    } catch (e) {
      debugPrint('Fehler beim Laden der Warengruppen: $e');

      if (mounted) {
        setState(() => _isLoadingCategories = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Fehler beim Laden der Warengruppen: $e')),
        );
      }
    }
  }

  // Dialog zum Hinzufügen oder Bearbeiten einer Warengruppe
  Future<void> _showCategoryDialog([ProductCategory? category]) async {
    final isEditing = category != null;
    final titleController =
        TextEditingController(text: isEditing ? category.name : '');
    String selectedIcon = isEditing ? category.iconName : 'shopping_bag';

    // Map von Icons für die Auswahl
    final Map<String, IconData> iconMap = {
      'shopping_bag': Icons.shopping_bag,
      'clothing': Icons.checkroom,
      'toys': Icons.toys,
      'books': Icons.book,
      'electronics': Icons.devices,
      'furniture': Icons.chair,
      'jewelry': Icons.diamond,
      'sports': Icons.sports_soccer,
      'food': Icons.fastfood,
      'tools': Icons.handyman,
      'art': Icons.palette,
      'music': Icons.music_note,
      'baby': Icons.child_care,
      'beauty': Icons.face,
      'garden': Icons.yard,
      'automotive': Icons.directions_car,
      'pets': Icons.pets,
      'office': Icons.business_center,
      'crafts': Icons.brush,
      'other': Icons.category,
    };

    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          insetPadding: EdgeInsets.symmetric(horizontal: 24.0, vertical: 24.0),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16.0),
          ),
          child: StatefulBuilder(
            builder: (BuildContext context, StateSetter setDialogState) {
              // MediaQuery verwenden, um die Tastaturhöhe zu ermitteln
              final bottomInset = MediaQuery.of(context).viewInsets.bottom;

              return Container(
                width: 400,
                // Maximale Höhe dynamisch anpassen, wenn Tastatur angezeigt wird
                constraints: BoxConstraints(
                  maxHeight: bottomInset > 0
                      ? MediaQuery.of(context).size.height * 0.5
                      : 500,
                ),
                padding: EdgeInsets.all(16),
                // SingleChildScrollView hinzufügen, um scrollbar zu machen
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // HEADER
                      Text(
                        isEditing
                            ? 'Warengruppe bearbeiten'
                            : 'Neue Warengruppe',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      SizedBox(height: 16),

                      // NAME FIELD
                      TextField(
                        controller: titleController,
                        decoration: InputDecoration(
                          labelText: 'Name der Warengruppe',
                          hintText: 'z.B. Kleidung, Spielzeug, Bücher...',
                          border: OutlineInputBorder(),
                        ),
                        autofocus: true,
                      ),
                      SizedBox(height: 16),

                      // ICON SELECTION TEXT
                      Text(
                        'Icon auswählen:',
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      SizedBox(height: 8),

                      // ICON GRID - Ohne Flexible-Widget, da im ScrollView
                      Container(
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Padding(
                          padding: EdgeInsets.all(4),
                          child: Wrap(
                            spacing: 8,
                            runSpacing: 8,
                            children: iconMap.entries.map((entry) {
                              final iconName = entry.key;
                              final iconData = entry.value;
                              final isSelected = selectedIcon == iconName;

                              return InkWell(
                                onTap: () {
                                  setDialogState(() {
                                    selectedIcon = iconName;
                                  });
                                },
                                borderRadius: BorderRadius.circular(8),
                                child: Container(
                                  width: 48,
                                  height: 48,
                                  decoration: BoxDecoration(
                                    color: isSelected
                                        ? Theme.of(context)
                                            .colorScheme
                                            .primary
                                            .withOpacity(0.2)
                                        : Colors.transparent,
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(
                                      color: isSelected
                                          ? Theme.of(context)
                                              .colorScheme
                                              .primary
                                          : Colors.grey.shade300,
                                      width: isSelected ? 2 : 1,
                                    ),
                                  ),
                                  child: Center(
                                    child: Icon(
                                      iconData,
                                      color: isSelected
                                          ? Theme.of(context)
                                              .colorScheme
                                              .primary
                                          : Colors.grey.shade700,
                                      size: 24,
                                    ),
                                  ),
                                ),
                              );
                            }).toList(),
                          ),
                        ),
                      ),

                      SizedBox(height: 16),

                      // BUTTONS
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          TextButton(
                            onPressed: () => Navigator.of(context).pop(),
                            child: Text('Abbrechen'),
                          ),
                          SizedBox(width: 8),
                          ElevatedButton(
                            onPressed: () async {
                              final name = titleController.text.trim();
                              if (name.isEmpty) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content:
                                        Text('Bitte geben Sie einen Namen ein'),
                                  ),
                                );
                                return;
                              }

                              Navigator.of(context).pop();

                              setState(() => _isLoadingCategories = true);
                              try {
                                if (isEditing) {
                                  // Bestehende Kategorie aktualisieren
                                  final updatedCategory = category.copyWith(
                                    name: name,
                                    iconName: selectedIcon,
                                  );
                                  await _productCategoryService
                                      .updateCategory(updatedCategory);
                                } else {
                                  // Neue Kategorie erstellen
                                  await _productCategoryService.addCategory(
                                    name: name,
                                    iconName: selectedIcon,
                                  );
                                }
                                // Kategorien neu laden
                                await _loadCategories();
                              } catch (e) {
                                debugPrint(
                                    'Fehler beim Speichern der Warengruppe: $e');
                                if (mounted) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                        content:
                                            Text('Fehler beim Speichern: $e')),
                                  );
                                  setState(() => _isLoadingCategories = false);
                                }
                              }
                            },
                            child: Text(
                                isEditing ? 'Aktualisieren' : 'Hinzufügen'),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  // Bestätigungsdialog zum Löschen einer Warengruppe
  Future<void> _showDeleteConfirmationDialog(ProductCategory category) async {
    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Warengruppe löschen?'),
          content: Text(
              'Möchten Sie die Warengruppe "${category.name}" wirklich löschen? '
              'Diese Aktion kann nicht rückgängig gemacht werden.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('Abbrechen'),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();

                setState(() => _isLoadingCategories = true);
                try {
                  await _productCategoryService.deleteCategory(category.id);
                  // Kategorien neu laden
                  await _loadCategories();
                } catch (e) {
                  debugPrint('Fehler beim Löschen der Warengruppe: $e');
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('Fehler beim Löschen: $e')),
                    );
                    setState(() => _isLoadingCategories = false);
                  }
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red.shade700,
                foregroundColor: Colors.white,
              ),
              child: Text('Löschen'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Einstellungen'),
      ),
      drawer: DrawerMenu(),
      body: ListView(
        padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 8.0),
        children: [
          // --- Abschnitt: Konto & Abonnement ---
          Padding(
            padding: const EdgeInsets.only(left: 16.0, bottom: 8.0),
            child: Text(
              'KONTO & ABONNEMENT',
              style: theme.textTheme.labelSmall?.copyWith(
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Card(
            elevation: 0,
            margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12.0),
              side: BorderSide(color: Colors.grey.shade300, width: 1.0),
            ),
            child: Column(
              children: [
                ListTile(
                  leading: Icon(Icons.account_circle_outlined,
                      color: theme.iconTheme.color),
                  title:
                      Text('Mein Profil', style: theme.textTheme.titleMedium),
                  subtitle: Text('Anzeigename, E-Mail, Abmelden',
                      style: theme.textTheme.bodySmall),
                  trailing: Icon(Icons.chevron_right,
                      color: theme.iconTheme.color?.withOpacity(0.6)),
                  onTap: () {
                    Navigator.pushNamed(context, '/profile');
                  },
                ),
                Divider(height: 1, thickness: 1, indent: 16, endIndent: 16),
                ListTile(
                  leading:
                      Icon(Icons.card_membership, color: theme.iconTheme.color),
                  title: Text('Abonnement verwalten',
                      style: theme.textTheme.titleMedium),
                  subtitle: Text('Upgrade, Gerätelimits und Zahlungsoptionen',
                      style: theme.textTheme.bodySmall),
                  trailing: Icon(Icons.chevron_right,
                      color: theme.iconTheme.color?.withOpacity(0.6)),
                  onTap: () {
                    Navigator.pushNamed(context, '/subscription');
                  },
                ),
              ],
            ),
          ),

          // --- Abschnitt: Geräte & Warengruppen ---
          Divider(height: 32, thickness: 1, indent: 8, endIndent: 8),
          Padding(
            padding: const EdgeInsets.only(left: 16.0, bottom: 8.0, top: 8.0),
            child: Text(
              'GERÄTE & WARENGRUPPEN',
              style: theme.textTheme.labelSmall?.copyWith(
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Card(
            elevation: 0,
            margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12.0),
              side: BorderSide(color: Colors.grey.shade300, width: 1.0),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Geräteverwaltung
                ListTile(
                  leading: Icon(Icons.devices, color: theme.iconTheme.color),
                  title: Text('Geräteverwaltung',
                      style: theme.textTheme.titleMedium),
                  subtitle: Text(
                      'Geräte aktivieren, deaktivieren und umbenennen',
                      style: theme.textTheme.bodySmall),
                  trailing: Icon(Icons.chevron_right,
                      color: theme.iconTheme.color?.withOpacity(0.6)),
                  onTap: () {
                    Navigator.pushNamed(context, '/name_device');
                  },
                ),

                // Trennlinie zwischen Geräten und Warengruppen
                Divider(height: 1, thickness: 1, indent: 16, endIndent: 16),

                // Warengruppen-Schalter und Button
                Row(
                  children: [
                    Expanded(
                      child: SwitchListTile(
                        title: Text('Warengruppen verwenden',
                            style: theme.textTheme.titleMedium),
                        subtitle: Text(
                            'Aktiviert die Auswahl von Warengruppen beim Erfassen von Verkäufen',
                            style: theme.textTheme.bodySmall),
                        value: _useCategoriesFeature,
                        activeColor: theme.colorScheme.primary,
                        secondary: Icon(Icons.category, color: theme.iconTheme.color),
                        onChanged: (value) async {
                          setState(() => _useCategoriesFeature = value);
                          await _saveSettings();

                          if (value) {
                            // Wenn aktiviert, Kategorien laden
                            _loadCategories();
                          }
                        },
                      ),
                    ),
                    // Nur anzeigen, wenn Warengruppen aktiviert sind
                    if (_useCategoriesFeature)
                      Padding(
                        padding: const EdgeInsets.only(right: 16.0),
                        child: Container(
                          height: 36, // 25% kleiner als 48
                          width: 36, // Quadratisch bleiben
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: theme.colorScheme.primary,
                          ),
                          child: IconButton(
                            icon:
                                Icon(Icons.add, color: Colors.white, size: 18),
                            onPressed: () => _showCategoryDialog(),
                            tooltip: 'Neue Warengruppe hinzufügen',
                            padding: EdgeInsets
                                .zero, // Kein Padding für bessere Zentrierung
                          ),
                        ),
                      ),
                  ],
                ),

                // Wenn Warengruppen aktiviert sind, Verwaltungsbereich anzeigen
                if (_useCategoriesFeature) ...[
                  // Überschrift mit Expander-Button
                  InkWell(
                    onTap: () {
                      setState(() {
                        _isCategoriesSectionExpanded =
                            !_isCategoriesSectionExpanded;
                      });
                    },
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 8.0),
                      child: Row(
                        children: [
                          Expanded(
                            child: Text(
                              'Verfügbare Warengruppen',
                              style: theme.textTheme.titleMedium,
                            ),
                          ),
                          Icon(
                            _isCategoriesSectionExpanded
                                ? Icons.expand_less
                                : Icons.expand_more,
                            color: theme.colorScheme.primary,
                          ),
                        ],
                      ),
                    ),
                  ),

                  // Kategorie-Grid nur anzeigen, wenn erweitert UND Kategorien vorhanden
                  if (_isCategoriesSectionExpanded) ...[
                    if (_isLoadingCategories)
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 24.0),
                        child: Center(child: CircularProgressIndicator()),
                      )
                    else if (_categories.isEmpty)
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Center(
                          child: Text(
                            'Keine Warengruppen vorhanden. Fügen Sie welche hinzu!',
                            textAlign: TextAlign.center,
                            style: TextStyle(color: Colors.grey),
                          ),
                        ),
                      )
                    else
                      Padding(
                        padding: const EdgeInsets.fromLTRB(8.0, 8.0, 8.0, 16.0),
                        child: GridView.builder(
                          physics: NeverScrollableScrollPhysics(),
                          shrinkWrap: true,
                          gridDelegate:
                              SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 3,
                            childAspectRatio: 0.8,
                            crossAxisSpacing: 4,
                            mainAxisSpacing: 8,
                          ),
                          itemCount: _categories.length,
                          itemBuilder: (context, index) {
                            final category = _categories[index];
                            return Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: Colors.grey.shade200,
                                  width: 1.0,
                                ),
                              ),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  // Icon mit Hintergrund
                                  Container(
                                    width: 40,
                                    height: 40,
                                    decoration: BoxDecoration(
                                      color: theme.colorScheme.primary
                                          .withOpacity(0.1),
                                      shape: BoxShape.circle,
                                    ),
                                    child: Center(
                                      child: Icon(
                                        category.getIconData(),
                                        color: theme.colorScheme.primary,
                                        size: 16,
                                      ),
                                    ),
                                  ),
                                  SizedBox(height: 6),
                                  // Name der Kategorie
                                  Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 4.0),
                                    child: Text(
                                      category.name,
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        fontWeight: FontWeight.w500,
                                        fontSize: 13,
                                      ),
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                  SizedBox(height: 8), // Mehr Abstand zum Namen
                                  // Aktionen als Zeile mit IconButtons in Kreisen
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      // Bearbeiten-Button im Kreis
                                      Container(
                                        width: 24,
                                        height: 24,
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          color: Colors.transparent,
                                          border: Border.all(
                                            color: theme.colorScheme.primary,
                                            width: 1.0,
                                          ),
                                        ),
                                        child: IconButton(
                                          icon: Icon(
                                            Icons.edit_outlined,
                                            size: 14,
                                            color: theme.colorScheme.primary,
                                          ),
                                          onPressed: () =>
                                              _showCategoryDialog(category),
                                          padding: EdgeInsets.zero,
                                          constraints: BoxConstraints(),
                                          tooltip: 'Bearbeiten',
                                        ),
                                      ),
                                      SizedBox(
                                          width:
                                              8), // Mehr Abstand zwischen den Buttons
                                      // Löschen-Button im Kreis
                                      Container(
                                        width: 24,
                                        height: 24,
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          color: Colors.transparent,
                                          border: Border.all(
                                            color: Colors.red.shade300,
                                            width: 1.0,
                                          ),
                                        ),
                                        child: IconButton(
                                          icon: Icon(
                                            Icons.delete_outline,
                                            size: 14,
                                            color: Colors.red.shade300,
                                          ),
                                          onPressed: () =>
                                              _showDeleteConfirmationDialog(
                                                  category),
                                          padding: EdgeInsets.zero,
                                          constraints: BoxConstraints(),
                                          tooltip: 'Löschen',
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                      ),
                  ],
                ],
              ],
            ),
          ),

          // --- Abschnitt: Anzeige (NEU) ---
          Divider(height: 32, thickness: 1, indent: 8, endIndent: 8),
          Padding(
            padding: const EdgeInsets.only(left: 16.0, bottom: 8.0, top: 8.0),
            child: Text(
              'ANZEIGE',
              style: theme.textTheme.labelSmall?.copyWith(
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Card(
            elevation: 0,
            margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12.0),
              side: BorderSide(color: Colors.grey.shade300, width: 1.0),
            ),
            child: SwitchListTile(
              title: Text('Vollbildmodus', style: theme.textTheme.titleMedium),
              subtitle: Text(
                'App im Vollbild anzeigen (Statusleiste ausblenden)',
                style: theme.textTheme.bodySmall,
              ),
              secondary: Icon(Icons.fullscreen, color: theme.iconTheme.color),
              value: _isFullscreenEnabled,
              activeColor: theme.colorScheme.primary,
              onChanged: (value) async {
                setState(() => _isFullscreenEnabled = value);
                await _saveSettings();
                _applyFullscreenMode();
              },
            ),
          ),

          // --- Abschnitt: Hilfe & Info ---
          Divider(height: 32, thickness: 1, indent: 8, endIndent: 8),
          Padding(
            padding: const EdgeInsets.only(left: 16.0, bottom: 8.0, top: 8.0),
            child: Text(
              'HILFE & INFO',
              style: theme.textTheme.labelSmall?.copyWith(
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

          // Card für Hilfe & Info
          Card(
            elevation: 0,
            margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12.0),
              side: BorderSide(color: Colors.grey.shade300, width: 1.0),
            ),
            child: Column(
              children: [
                // Häufig gestellte Fragen
                ListTile(
                  leading: Icon(Icons.help_outline,
                      color: theme.colorScheme.primary),
                  title: Text('Häufig gestellte Fragen',
                      style: theme.textTheme.titleMedium),
                  subtitle: Text('Antworten auf allgemeine Fragen',
                      style: theme.textTheme.bodySmall),
                  trailing: Icon(Icons.chevron_right,
                      color: theme.iconTheme.color?.withOpacity(0.6)),
                  onTap: () {
                    // TODO: FAQ implementieren
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('FAQ wird noch implementiert')),
                    );
                  },
                ),

                // Onboarding zurücksetzen
                ShowcaseHelper.buildShowcase(
                  key: _onboardingResetKey,
                  title: 'Onboarding zurücksetzen',
                  description:
                      'Hier können Sie das Onboarding zurücksetzen, um die Einführung erneut anzuzeigen.',
                  child: ListTile(
                    leading: Icon(Icons.restart_alt,
                        color: theme.colorScheme.primary),
                    title: Text('Onboarding zurücksetzen',
                        style: theme.textTheme.titleMedium),
                    subtitle: Text('Starten Sie die Einführung erneut',
                        style: theme.textTheme.bodySmall),
                    trailing: Icon(Icons.chevron_right,
                        color: theme.iconTheme.color?.withOpacity(0.6)),
                    onTap: _resetOnboarding,
                  ),
                ),

                // Erweiterte Optionen
                ListTile(
                  leading:
                      Icon(Icons.more_horiz, color: theme.colorScheme.primary),
                  title: Text('Erweiterte Optionen',
                      style: theme.textTheme.titleMedium),
                  subtitle: Text(
                      'Datenschutz, Nutzungsbedingungen und Lizenzen',
                      style: theme.textTheme.bodySmall),
                  trailing: Icon(Icons.chevron_right,
                      color: theme.iconTheme.color?.withOpacity(0.6)),
                  onTap: () => _showAdvancedOptionsModal(context),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
