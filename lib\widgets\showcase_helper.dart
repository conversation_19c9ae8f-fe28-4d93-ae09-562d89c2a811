import 'package:flutter/material.dart';
import 'package:showcaseview/showcaseview.dart';
import '../utils/onboarding_style_helper.dart';

/// Hilfsfunktionen und Widgets für die Erstellung von Showcase-Overlays
/// mit einheitlichem Design für alle Onboarding-Elemente
class ShowcaseHelper {
  /// Erstellt ein Showcase-Widget mit einheitlichem Stil
  static Showcase buildShowcase({
    required GlobalKey key,
    required String title,
    required String description,
    required Widget child,
    BuildContext? context,
    TooltipPosition tooltipPosition = TooltipPosition.bottom,
    VoidCallback? onToolTipClick,
    VoidCallback? onTargetClick,
    bool? disposeOnTap,
  }) {
    // Wenn disposeOnTap true ist, aber kein onTargetClick definiert wurde,
    // erstellen wir eine leere Funktion, um den Fehler zu vermeiden
    final effectiveOnTargetClick =
        (disposeOnTap ?? true) && onTargetClick == null
            ? () {} // Leere Funktion als Fallback
            : onTargetClick;

    // Wir verwenden feste Werte für ein einheitliches Erscheinungsbild

    return Showcase(
      key: key,
      title: title,
      description: description,
      tooltipPosition: tooltipPosition,
      tooltipBackgroundColor: Colors.blueGrey.shade700, // Einheitliche Farbe für alle Tooltips
      textColor: Colors.white,
      tooltipPadding: const EdgeInsets.all(16.0),
      titleTextStyle: TextStyle(
        fontWeight: FontWeight.bold,
        fontSize: 18.0,
        color: Colors.white,
      ),
      descTextStyle: TextStyle(
        fontSize: 15.0,
        color: Colors.white,
      ),
      overlayColor: Colors.black.withAlpha(179), // 0.7 * 255 = 179
      overlayOpacity: 0.7,
      targetShapeBorder: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0), // Einheitlicher Radius
      ),
      targetPadding: const EdgeInsets.all(8.0),
      showArrow: true,
      onToolTipClick: onToolTipClick,
      onTargetClick: effectiveOnTargetClick,
      disposeOnTap: disposeOnTap ?? true,
      child: child,
    );
  }

  /// Zeigt eine Reihe von Showcase-Widgets an
  static void showMultipleShowcase(
    BuildContext context,
    List<GlobalKey> keys, {
    VoidCallback? onFinish,
  }) {
    if (keys.isEmpty) return;

    // Showcase starten
    ShowCaseWidget.of(context).startShowCase(keys);

    // Wenn ein onFinish-Callback vorhanden ist, verzögert ausführen
    if (onFinish != null) {
      // Schätzen der Dauer basierend auf der Anzahl der Showcase-Elemente
      final estimatedDuration = Duration(seconds: keys.length * 4);
      Future.delayed(estimatedDuration, onFinish);
    }
  }

  /// Erstellt ein ShowCaseWidget als Wrapper für die gesamte App oder einen Screen
  static Widget buildShowCaseWidget({
    required Widget child,
    bool autoPlay = false,
    Duration autoPlayDelay = const Duration(seconds: 3),
  }) {
    return ShowCaseWidget(
      autoPlay: autoPlay,
      autoPlayDelay: autoPlayDelay,
      builder: (context) => child,
    );
  }
  
  /// Erstellt einen Container im einheitlichen Onboarding-Stil
  static Widget buildStyledContainer({
    required BuildContext context,
    required Widget child,
    EdgeInsets? padding,
  }) {
    return OnboardingStyleHelper.buildOnboardingCard(
      context: context,
      padding: padding,
      child: child,
    );
  }
  
  /// Erstellt einen Button im einheitlichen Onboarding-Stil
  static Widget buildStyledButton({
    required BuildContext context,
    required String text,
    required VoidCallback onPressed,
    bool isPrimary = true,
  }) {
    return OnboardingStyleHelper.buildOnboardingButton(
      context: context,
      text: text,
      onPressed: onPressed,
      isPrimary: isPrimary,
    );
  }
}
