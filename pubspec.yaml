name: flohmarkt_kassiersystem
description: Ein Android Kassenbuch zum Erfassen von Verkäufen auf Trödelmärkten.
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: ">=2.16.0 <3.0.0"

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.2
  transformable_list_view: ^0.7.0
  # Google Auth und Sheets Integration
  google_sign_in: ^6.1.0
  googleapis: ^11.0.0
  googleapis_auth: ^1.4.0
  connectivity_plus: ^4.0.2
  google_fonts: ^6.2.1
  # Firebase Core (immer benötigt)
  firebase_core: ^3.13.0
  # Firestore Datenbank
  cloud_firestore: ^5.6.6
  # Firebase Auth (wird oft benötigt, auch wenn Google Sign-In separat ist)
  firebase_auth: ^5.5.2

  # Lokale Datenbank
  sqflite: ^2.2.0+3
  path_provider: ^2.0.14
  path: ^1.8.2

  # State Management
  provider: ^6.0.5

  # Lokale Speicherung
  shared_preferences: ^2.1.0
  flutter_secure_storage: ^8.0.0

  # HTTP Kommunikation
  http: ^1.1.0

  # Utility Packages
  intl: ^0.18.0
  uuid: ^3.0.7

  # UI-Komponenten
  flutter_spinkit: ^5.2.0

  # Excel und Datei-Sharing
  excel: ^2.1.0
  share_plus: ^7.0.2

  # In-App-Purchases und Abonnements
  in_app_purchase: ^3.1.10

  # Kamera und Text-Erkennung
  camera: ^0.10.5+9
  google_mlkit_text_recognition: ^0.15.0
  google_mlkit_digital_ink_recognition: ^0.14.1

  # Bild-Verarbeitung (neu)
  image: ^4.1.3
  rxdart: ^0.28.0

  # Onboarding und Feature-Entdeckung
  showcaseview: ^4.0.1
  permission_handler: ^11.0.1
  dots_indicator: ^3.0.0
  url_launcher: ^6.3.1


dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0

flutter:
  uses-material-design: true

  # Assets für die App
  assets:
    - assets/images/

  # Auskommentiert, bis die Schriftarten hinzugefügt werden
  # fonts:
  #   - family: Roboto
  #     fonts:
  #       - asset: assets/fonts/Roboto-Regular.ttf
  #       - asset: assets/fonts/Roboto-Bold.ttf
  #         weight: 700
  #       - asset: assets/fonts/Roboto-Light.ttf
  #         weight: 300