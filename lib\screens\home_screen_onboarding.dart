import 'package:flutter/material.dart';
import 'package:showcaseview/showcaseview.dart';
import '../widgets/showcase_helper.dart';
import '../services/onboarding_service.dart';
import '../models/flea_market.dart';

/// Klasse zur Verwaltung des HomeScreen-Onboardings mit einheitlichem Design
class HomeScreenOnboarding {
  final BuildContext context;
  final GlobalKey cashierKey = GlobalKey();
  final GlobalKey inputAreaKey = GlobalKey();
  final GlobalKey cartKey = GlobalKey();
  final GlobalKey paymentAreaKey = GlobalKey();
  final GlobalKey marketStatusKey = GlobalKey();
  final GlobalKey menuKey = GlobalKey();
  final FleaMarket? currentFleaMarket;

  HomeScreenOnboarding({
    required this.context,
    this.currentFleaMarket,
  });

  /// Startet das HomeScreen-Onboarding
  Future<void> startOnboarding() async {
    final onboardingService = OnboardingService();
    final isCompleted = await onboardingService.isHomeScreenOnboardingCompleted();

    if (isCompleted) {
      debugPrint('HomeScreenOnboarding: Onboarding bereits abgeschlossen');
      return;
    }

    // Warte einen Moment, damit die UI vollständig gerendert ist
    await Future.delayed(const Duration(milliseconds: 500));

    // Starte das Onboarding
    _showHomeScreenOverview();
  }

  /// Zeigt den Überblick über den HomeScreen
  void _showHomeScreenOverview() {
    final keys = [cashierKey, inputAreaKey, cartKey, paymentAreaKey];
    
    ShowcaseHelper.showMultipleShowcase(
      context,
      keys,
      onFinish: () {
        // Nach dem Überblick die Markt-Status-Information anzeigen
        _showMarketStatusInformation();
      },
    );
  }

  /// Zeigt Informationen zum Trödelmarkt-Status
  void _showMarketStatusInformation() {
    final keys = [marketStatusKey];
    
    ShowcaseHelper.showMultipleShowcase(
      context,
      keys,
      onFinish: () {
        // Nach der Markt-Status-Information das Menü vorstellen
        _showMenuIntroduction();
      },
    );
  }

  /// Stellt das Menü vor
  void _showMenuIntroduction() {
    final keys = [menuKey];
    
    ShowcaseHelper.showMultipleShowcase(
      context,
      keys,
      onFinish: () {
        // Onboarding als abgeschlossen markieren
        _completeOnboarding();
      },
    );
  }

  /// Markiert das HomeScreen-Onboarding als abgeschlossen
  Future<void> _completeOnboarding() async {
    final onboardingService = OnboardingService();
    await onboardingService.setHomeScreenOnboardingCompleted(true);
    debugPrint('HomeScreenOnboarding: Onboarding abgeschlossen');
  }

  /// Baut die Showcase-Widgets für den HomeScreen mit einheitlichem Design
  List<Widget> buildShowcaseWidgets({
    required Widget cashierWidget,
    required Widget inputAreaWidget,
    required Widget cartWidget,
    required Widget paymentAreaWidget,
    required Widget marketStatusWidget,
    required Widget menuWidget,
  }) {
    return [
      // Kassierer-Auswahl
      ShowcaseHelper.buildShowcase(
        key: cashierKey,
        title: 'Kassierer-Auswahl',
        description: 'Hier wählen Sie den aktiven Kassierer aus, der die Buchungen durchführt.',
        child: cashierWidget,
        context: context, // Kontext für Theme-Zugriff
      ),
      
      // Eingabebereich
      ShowcaseHelper.buildShowcase(
        key: inputAreaKey,
        title: 'Eingabebereich',
        description: currentFleaMarket?.isNumberBasedMarket ?? false
            ? 'Geben Sie hier die Verkäufer-Nr. und den Preis ein. Bei Nummerntrödeln ist die Verkäufer-Nr. erforderlich.'
            : 'Geben Sie hier den Preis ein. Bei normalem Trödel ist keine Verkäufer-Nr. erforderlich.',
        child: inputAreaWidget,
        context: context,
      ),
      
      // Warenkorb
      ShowcaseHelper.buildShowcase(
        key: cartKey,
        title: 'Warenkorb',
        description: 'Hier werden alle Artikel angezeigt, die zum aktuellen Verkauf gehören.',
        child: cartWidget,
        context: context,
      ),
      
      // Zahlungsbereich
      ShowcaseHelper.buildShowcase(
        key: paymentAreaKey,
        title: 'Zahlungsbereich',
        description: 'Hier geben Sie den gezahlten Betrag ein und schließen den Verkauf ab.',
        child: paymentAreaWidget,
        context: context,
      ),
      
      // Trödelmarkt-Status
      ShowcaseHelper.buildShowcase(
        key: marketStatusKey,
        title: 'Trödelmarkt-Status',
        description: currentFleaMarket != null
            ? 'Aktuell aktiv: ${currentFleaMarket!.name}'
            : 'Kein Markt aktiv! Buchungen sind nur bei aktivem Trödelmarkt möglich.',
        child: marketStatusWidget,
        tooltipPosition: TooltipPosition.top,
        context: context,
      ),
      
      // Menü
      ShowcaseHelper.buildShowcase(
        key: menuKey,
        title: 'Menü',
        description: 'Über das Menü gelangen Sie zu allen Verwaltungsfunktionen: Trödelmärkte, Verkäufer, Kassierer, Auswertungen, Einstellungen',
        child: menuWidget,
        tooltipPosition: TooltipPosition.bottom,
        context: context,
      ),
    ];
  }
}
