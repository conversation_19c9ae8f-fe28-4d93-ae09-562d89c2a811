{"logs": [{"outputFile": "com.example.troedelpos.app-mergeDebugResources-55:/values-en-rAU/values-en-rAU.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fe930d2507b0279f955567b6d5748a32\\transformed\\core-1.13.1\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "31,32,33,34,35,36,37,47", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2988,3084,3186,3285,3384,3488,3591,4556", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "3079,3181,3280,3379,3483,3586,3702,4652"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\596dc2cdab8b9d2ef83e7d6e0e134b8a\\transformed\\appcompat-1.6.1\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,46", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,4473", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,4551"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3f6219dea7a69dbc6d2bf4921723a7d5\\transformed\\browser-1.8.0\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,153,250,359", "endColumns": "97,96,108,98", "endOffsets": "148,245,354,453"}, "to": {"startLines": "39,41,42,43", "startColumns": "4,4,4,4", "startOffsets": "3775,3960,4057,4166", "endColumns": "97,96,108,98", "endOffsets": "3868,4052,4161,4260"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a75011f7f88673231c312008696729f9\\transformed\\preference-1.2.1\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,173,260,334,468,637,717", "endColumns": "67,86,73,133,168,79,75", "endOffsets": "168,255,329,463,632,712,788"}, "to": {"startLines": "38,40,44,45,48,49,50", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3707,3873,4265,4339,4657,4826,4906", "endColumns": "67,86,73,133,168,79,75", "endOffsets": "3770,3955,4334,4468,4821,4901,4977"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\62aeb653dda202444616439a5b81d504\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,113", "endOffsets": "162,276"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2762,2874", "endColumns": "111,113", "endOffsets": "2869,2983"}}]}]}